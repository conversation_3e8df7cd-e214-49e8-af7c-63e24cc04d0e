import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function middleware(req: NextRequest) {
  // Protected routes that require authentication
  const protectedRoutes = ['/dashboard']

  // Auth routes that should redirect if already authenticated
  const authRoutes = ['/', '/sign-up', '/forgot-password']

  // Routes that should be excluded from middleware
  const excludedRoutes = ['/auth/callback', '/reset-password']

  const isProtectedRoute = protectedRoutes.some(route =>
    req.nextUrl.pathname.startsWith(route)
  )

  const isAuthRoute = authRoutes.includes(req.nextUrl.pathname)

  const isExcludedRoute = excludedRoutes.some(route =>
    req.nextUrl.pathname.startsWith(route)
  )

  // Skip middleware for excluded routes
  if (isExcludedRoute) {
    return NextResponse.next()
  }

  // Create a Supabase client for server-side session validation
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('Missing Supabase environment variables')
    return NextResponse.next()
  }

  const supabase = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })

  // Get session from request cookies
  let hasValidSession = false

  try {
    // Try to get session from cookies using Supabase's built-in method
    const authHeader = req.headers.get('authorization')
    const cookieHeader = req.headers.get('cookie')

    if (cookieHeader) {
      // Parse cookies to find Supabase auth tokens
      const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=')
        if (key && value) {
          acc[key] = decodeURIComponent(value)
        }
        return acc
      }, {} as Record<string, string>)

      // Look for any Supabase auth token cookies
      const authTokenKeys = Object.keys(cookies).filter(key =>
        key.includes('supabase') && key.includes('auth-token') && !key.includes('code-verifier')
      )

      if (authTokenKeys.length > 0) {
        // Found at least one auth token cookie
        hasValidSession = true
      }
    }
  } catch (error) {
    console.error('Error checking session:', error)
    hasValidSession = false
  }

  // If accessing a protected route without a valid session, redirect to sign in
  if (isProtectedRoute && !hasValidSession) {
    const redirectUrl = new URL('/', req.url)
    redirectUrl.searchParams.set('redirectTo', req.nextUrl.pathname)
    return NextResponse.redirect(redirectUrl)
  }

  // If accessing auth routes with a valid session, redirect to dashboard
  if (isAuthRoute && hasValidSession) {
    return NextResponse.redirect(new URL('/dashboard', req.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
