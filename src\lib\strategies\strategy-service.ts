/**
 * Trading Strategy Service
 * Manages strategy execution, signal generation, and database integration
 */

import { EmaScalper, Candle } from './emaScalper';
import { ORBStrategy, ORBSignal, ORBConfig, Candle as OR<PERSON>andle } from './orbStrategy';
import { ProcessedMarketData } from '@/types/dhan';
import { supabase } from '@/lib/supabase';
import { tradingLogger } from '@/lib/trading-logger';

interface DbTradingSignal {
  id?: string;
  user_id: string;
  signal_type: 'BUY' | 'SELL';
  instrument_symbol: string;
  instrument_name: string;
  signal_price: string;
  strategy_name: string;
  strategy_params: Record<string, unknown>;
  market_data?: Record<string, unknown>;
  candle_index?: number;
  ema_value?: string;
  last_8h_high?: string;
  last_8l_low?: string;
  is_executed: boolean;
  execution_attempted: boolean;
  auto_trade_enabled: boolean;
  trading_mode: 'SANDBOX' | 'LIVE';
  created_at?: string;
}

export interface StrategyConfig {
  name: 'EMA_SCALPER' | 'ORB';
  enabled: boolean;
  // EMA Scalper specific
  emaLength?: number;
  lookbackPeriod?: number;
  // ORB specific
  orbConfig?: ORBConfig;
}

export interface TradingSignal {
  id?: string;
  userId: string;
  signalType: 'BUY' | 'SELL';
  instrumentSymbol: string;
  instrumentName: string;
  signalPrice: number;
  strategyName: string;
  strategyParams: Record<string, unknown>;
  marketData?: Record<string, unknown>;
  candleIndex?: number;
  emaValue?: number;
  last8hHigh?: number;
  last8lLow?: number;
  isExecuted: boolean;
  executionAttempted: boolean;
  autoTradeEnabled: boolean;
  tradingMode: 'SANDBOX' | 'LIVE';
  createdAt?: string;
}

export interface MarketDataPoint {
  timestamp: Date;
  price: number;
  symbol: string;
  volume?: number;
}

export class StrategyService {
  private emaScalper: EmaScalper;
  private orbStrategy: ORBStrategy;
  private priceHistory: Map<string, MarketDataPoint[]> = new Map();
  private candleHistory: Map<string, ORBCandle[]> = new Map();
  private lastSignalTime: Map<string, Date> = new Map();
  private readonly MIN_SIGNAL_INTERVAL = 60000; // 1 minute minimum between signals
  private currentConfig: StrategyConfig;

  constructor(config: StrategyConfig = { name: 'EMA_SCALPER', emaLength: 20, lookbackPeriod: 8, enabled: true }) {
    this.currentConfig = config;
    this.emaScalper = new EmaScalper(config.emaLength || 20);
    this.orbStrategy = new ORBStrategy(config.orbConfig);
  }

  /**
   * Process new market data and generate signals
   */
  async processMarketData(marketData: ProcessedMarketData, userId: string): Promise<TradingSignal[]> {
    const signals: TradingSignal[] = [];

    try {
      // Get user's trading configuration
      const config = await this.getTradingConfig(userId);
      if (!config || !config.auto_trade_enabled) {
        return signals;
      }

      // Update price and candle history
      this.updatePriceHistory(marketData);
      this.updateCandleHistory(marketData);

      // Check if enough time has passed since last signal
      const lastSignal = this.lastSignalTime.get(marketData.symbol);
      if (lastSignal && Date.now() - lastSignal.getTime() < this.MIN_SIGNAL_INTERVAL) {
        return signals;
      }

      // Process based on strategy type
      if (this.currentConfig.name === 'EMA_SCALPER') {
        const emaSignals = await this.processEMAStrategy(marketData, userId, config);
        signals.push(...emaSignals);
      } else if (this.currentConfig.name === 'ORB') {
        const orbSignals = await this.processORBStrategy(marketData, userId, config);
        signals.push(...orbSignals);
      }

    } catch (error) {
      console.error('Error processing market data for strategy:', error);
    }

    return signals;
  }

  /**
   * Process EMA Scalper strategy
   */
  private async processEMAStrategy(marketData: ProcessedMarketData, userId: string, config: any): Promise<TradingSignal[]> {
    const signals: TradingSignal[] = [];

    // Get price history for this instrument
    const history = this.priceHistory.get(marketData.symbol);
    if (!history || history.length < 20) {
      // Need at least 20 data points for EMA calculation
      return signals;
    }

    // Convert to candle format
    const candles: Candle[] = history.map(point => ({ close: point.price }));

    // Run strategy
    const strategySignals = this.emaScalper.runStrategy(candles);

    // Process only the latest signal
    if (strategySignals.length > 0) {
      const latestSignal = strategySignals[strategySignals.length - 1];

      if (latestSignal.type) {
        const tradingSignal: TradingSignal = {
          userId,
          signalType: latestSignal.type,
          instrumentSymbol: marketData.symbol,
          instrumentName: marketData.name,
          signalPrice: latestSignal.price,
          strategyName: 'EMA_SCALPER',
          strategyParams: {
            emaLength: this.emaScalper['length'],
            lookbackPeriod: 8,
            candleIndex: latestSignal.index
          },
          marketData: {
            currentPrice: marketData.currentPrice,
            change: marketData.change,
            changePercent: marketData.changePercent,
            volume: marketData.volume,
            lastUpdated: marketData.lastUpdated
          },
          candleIndex: latestSignal.index,
          emaValue: this.emaScalper['emaValues'][latestSignal.index],
          isExecuted: false,
          executionAttempted: false,
          autoTradeEnabled: config.auto_trade_enabled,
          tradingMode: config.trading_mode
        };

        // Calculate last 8 high/low
        const recentPrices = history.slice(-8).map(p => p.price);
        tradingSignal.last8hHigh = Math.max(...recentPrices);
        tradingSignal.last8lLow = Math.min(...recentPrices);

        signals.push(tradingSignal);

        // Update last signal time
        this.lastSignalTime.set(marketData.symbol, new Date());

        // Log signal generation
        tradingLogger.logSignal(userId, tradingSignal, {
          marketData: {
            currentPrice: marketData.currentPrice,
            change: marketData.change,
            volume: marketData.volume
          }
        });

        // Store signal in database
        await this.storeSignal(tradingSignal);
      }
    }

    return signals;
  }

  /**
   * Process ORB strategy
   */
  private async processORBStrategy(marketData: ProcessedMarketData, userId: string, config: any): Promise<TradingSignal[]> {
    const signals: TradingSignal[] = [];

    // Get candle history for this instrument
    const candles = this.candleHistory.get(marketData.symbol);
    if (!candles || candles.length < 10) {
      // Need at least 10 candles for ORB calculation
      return signals;
    }

    // Run ORB strategy
    const orbSignals = this.orbStrategy.runStrategy(candles);

    // Process latest signals
    if (orbSignals.length > 0) {
      for (const orbSignal of orbSignals.slice(-3)) { // Process last 3 signals to catch TP/SL
        if (orbSignal.type && orbSignal.type !== null) {
          const tradingSignal: TradingSignal = {
            userId,
            signalType: this.mapORBSignalType(orbSignal.type),
            instrumentSymbol: marketData.symbol,
            instrumentName: marketData.name,
            signalPrice: orbSignal.price,
            strategyName: 'ORB',
            strategyParams: {
              ...this.currentConfig.orbConfig,
              candleIndex: orbSignal.index,
              level: orbSignal.level,
              bullish: orbSignal.bullish
            },
            marketData: {
              currentPrice: marketData.currentPrice,
              change: marketData.change,
              changePercent: marketData.changePercent,
              volume: marketData.volume,
              lastUpdated: marketData.lastUpdated
            },
            candleIndex: orbSignal.index,
            isExecuted: false,
            executionAttempted: false,
            autoTradeEnabled: config.auto_trade_enabled,
            tradingMode: config.trading_mode
          };

          signals.push(tradingSignal);

          // Log signal generation
          tradingLogger.logSignal(userId, tradingSignal, {
            marketData: {
              currentPrice: marketData.currentPrice,
              change: marketData.change,
              volume: marketData.volume
            }
          });

          // Store signal in database
          await this.storeSignal(tradingSignal);
        }
      }

      if (signals.length > 0) {
        // Update last signal time
        this.lastSignalTime.set(marketData.symbol, new Date());
      }
    }

    return signals;
  }

  /**
   * Update price history for an instrument
   */
  private updatePriceHistory(marketData: ProcessedMarketData): void {
    const symbol = marketData.symbol;
    
    if (!this.priceHistory.has(symbol)) {
      this.priceHistory.set(symbol, []);
    }

    const history = this.priceHistory.get(symbol)!;
    const newPoint: MarketDataPoint = {
      timestamp: marketData.lastUpdated || new Date(),
      price: marketData.currentPrice,
      symbol: marketData.symbol,
      volume: marketData.volume
    };

    // Add new point
    history.push(newPoint);

    // Keep only last 100 points to manage memory
    if (history.length > 100) {
      history.shift();
    }

    // Remove duplicate timestamps (keep latest)
    const uniqueHistory = history.filter((point, index, arr) => {
      return index === arr.length - 1 || 
             point.timestamp.getTime() !== arr[index + 1].timestamp.getTime();
    });

    this.priceHistory.set(symbol, uniqueHistory);
  }

  /**
   * Update candle history for ORB strategy
   */
  private updateCandleHistory(marketData: ProcessedMarketData): void {
    const symbol = marketData.symbol;

    if (!this.candleHistory.has(symbol)) {
      this.candleHistory.set(symbol, []);
    }

    const candles = this.candleHistory.get(symbol)!;
    const timestamp = (marketData.lastUpdated || new Date()).getTime();

    // For real-time data, we create synthetic candles
    // In a real implementation, you'd get proper OHLC data
    const candle: ORBCandle = {
      time: timestamp,
      open: marketData.currentPrice,
      high: marketData.currentPrice,
      low: marketData.currentPrice,
      close: marketData.currentPrice,
      volume: marketData.volume
    };

    // Update or add candle (simplified - in reality you'd aggregate by time periods)
    candles.push(candle);

    // Keep only last 200 candles to manage memory
    if (candles.length > 200) {
      candles.shift();
    }
  }

  /**
   * Map ORB signal types to trading signal types
   */
  private mapORBSignalType(orbType: string): 'BUY' | 'SELL' {
    switch (orbType) {
      case 'BUY':
      case 'TP1':
      case 'TP2':
      case 'TP3':
        return 'BUY';
      case 'SELL':
      case 'SL':
      case 'EXIT':
        return 'SELL';
      default:
        return 'BUY';
    }
  }

  /**
   * Update strategy configuration
   */
  updateStrategyConfig(config: StrategyConfig): void {
    this.currentConfig = config;

    if (config.name === 'EMA_SCALPER' && config.emaLength) {
      this.emaScalper = new EmaScalper(config.emaLength);
    } else if (config.name === 'ORB' && config.orbConfig) {
      this.orbStrategy = new ORBStrategy(config.orbConfig);
    }
  }

  /**
   * Get user's trading configuration
   */
  private async getTradingConfig(userId: string) {
    const { data, error } = await supabase
      .from('trading_configuration')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error fetching trading config:', error);
      return null;
    }

    return data;
  }

  /**
   * Store trading signal in database
   */
  private async storeSignal(signal: TradingSignal): Promise<string | null> {
    try {
      const { data, error } = await supabase
        .from('trading_signals')
        .insert({
          user_id: signal.userId,
          signal_type: signal.signalType,
          instrument_symbol: signal.instrumentSymbol,
          instrument_name: signal.instrumentName,
          signal_price: signal.signalPrice,
          strategy_name: signal.strategyName,
          strategy_params: signal.strategyParams,
          market_data: signal.marketData,
          candle_index: signal.candleIndex,
          ema_value: signal.emaValue,
          last_8h_high: signal.last8hHigh,
          last_8l_low: signal.last8lLow,
          is_executed: signal.isExecuted,
          execution_attempted: signal.executionAttempted,
          auto_trade_enabled: signal.autoTradeEnabled,
          trading_mode: signal.tradingMode
        })
        .select('id')
        .single();

      if (error) {
        console.error('Error storing signal:', error);
        return null;
      }

      // Update strategy performance
      await this.updateStrategyPerformance(signal.userId, signal);

      return data.id;
    } catch (error) {
      console.error('Error in storeSignal:', error);
      return null;
    }
  }

  /**
   * Update strategy performance metrics
   */
  private async updateStrategyPerformance(userId: string, signal: TradingSignal): Promise<void> {
    try {
      const { error } = await supabase.rpc('update_strategy_performance', {
        p_user_id: userId,
        p_strategy_name: signal.strategyName,
        p_instrument_symbol: signal.instrumentSymbol,
        p_signal_type: signal.signalType,
        p_trading_mode: signal.tradingMode
      });

      if (error) {
        console.error('Error updating strategy performance:', error);
      }
    } catch (error) {
      console.error('Error in updateStrategyPerformance:', error);
    }
  }

  /**
   * Get recent signals for a user
   */
  async getRecentSignals(userId: string, limit: number = 50): Promise<TradingSignal[]> {
    const { data, error } = await supabase
      .from('trading_signals')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching recent signals:', error);
      return [];
    }

    return data.map(this.mapDbSignalToTradingSignal);
  }

  /**
   * Map database signal to TradingSignal interface
   */
  private mapDbSignalToTradingSignal(dbSignal: DbTradingSignal): TradingSignal {
    return {
      id: dbSignal.id,
      userId: dbSignal.user_id,
      signalType: dbSignal.signal_type,
      instrumentSymbol: dbSignal.instrument_symbol,
      instrumentName: dbSignal.instrument_name,
      signalPrice: parseFloat(dbSignal.signal_price),
      strategyName: dbSignal.strategy_name,
      strategyParams: dbSignal.strategy_params,
      marketData: dbSignal.market_data,
      candleIndex: dbSignal.candle_index,
      emaValue: dbSignal.ema_value ? parseFloat(dbSignal.ema_value) : undefined,
      last8hHigh: dbSignal.last_8h_high ? parseFloat(dbSignal.last_8h_high) : undefined,
      last8lLow: dbSignal.last_8l_low ? parseFloat(dbSignal.last_8l_low) : undefined,
      isExecuted: dbSignal.is_executed,
      executionAttempted: dbSignal.execution_attempted,
      autoTradeEnabled: dbSignal.auto_trade_enabled,
      tradingMode: dbSignal.trading_mode,
      createdAt: dbSignal.created_at
    };
  }
}

// Export singleton instance
export const strategyService = new StrategyService();
