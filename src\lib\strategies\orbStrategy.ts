/**
 * ORB (Opening Range Breakout) Strategy Implementation
 * Adapted for integration with the existing trading platform
 * Original algorithm converted from Pine Script to TypeScript
 */

export interface Candle {
  time: number; // ms timestamp
  open: number;
  high: number;
  low: number;
  close: number;
  volume?: number;
}

export interface ORBSignal {
  type: 'BUY' | 'SELL' | 'TP1' | 'TP2' | 'TP3' | 'SL' | 'EXIT' | null;
  price: number;
  index: number;
  level?: 1 | 2 | 3; // for TP signals
  bullish?: boolean; // for entry signals
}

export interface ORBConfig {
  orbTime: "15" | "30" | "45" | "60" | "90" | "120" | "180" | "240"; // minutes
  sensitivity?: "High" | "Medium" | "Low" | "Lowest";
  breakoutCondition?: "Close" | "EMA";
  tpMethod?: "Dynamic" | "ATR";
  emaLength?: number;
  slMethod?: "Safer" | "Balanced" | "Risky" | "Fixed";
  adaptiveSL?: boolean;
  stopLossPercent?: number;
  atrTP1Mult?: number;
  atrTP2Mult?: number;
  atrTP3Mult?: number;
  atrPeriod?: number;
  minimumProfitPercent?: number;
  minimumProfitIncrementPercent?: number;
  customSessionEnabled?: boolean;
  customSession?: string; // format "HHMM-HHMM"
  debug?: boolean;
}

enum ORBState {
  OpeningRange = "Opening Range",
  WaitingForBreakouts = "Waiting For Breakouts",
  InBreakout = "In Breakout",
  EntryTaken = "Entry Taken",
}

interface Breakout {
  isBullish: boolean;
  startIndex: number;
  endIndex?: number;
  failed?: boolean;
  retests: number;
}

interface ORB {
  h: number;
  l: number;
  startTime: number;
  startIndex: number;
  endIndex?: number;
  state: ORBState;
  breakouts: Breakout[];
  foundEntryTick: boolean;
  entryBullish?: boolean;
  entryIndex?: number;
  entryPrice?: number;
  entryATR?: number;
  tp1Index?: number;
  tp1Price?: number;
  tp1FoundTick?: boolean;
  tp2Index?: number;
  tp2Price?: number;
  tp2FoundTick?: boolean;
  tp3Index?: number;
  tp3Price?: number;
  tp3FoundTick?: boolean;
  slIndex?: number;
  slPrice?: number;
  slFoundTick?: boolean;
  isExitTrade?: boolean;
  exitTradeFoundTick?: boolean;
}

export class ORBStrategy {
  private cfg: Required<ORBConfig>;
  private candles: Candle[] = [];
  private orbList: ORB[] = [];
  private emaValue?: number;
  private emaAlpha?: number;
  private atrValue?: number;
  private lastClose?: number;
  private lastBarTime?: number;
  private idx = -1;

  constructor(config?: ORBConfig) {
    const defaultConfig: Required<ORBConfig> = {
      orbTime: "30",
      sensitivity: "Medium",
      breakoutCondition: "Close",
      tpMethod: "Dynamic",
      emaLength: 9,
      slMethod: "Balanced",
      adaptiveSL: true,
      stopLossPercent: 1.0,
      atrTP1Mult: 0.75,
      atrTP2Mult: 1.5,
      atrTP3Mult: 2.25,
      atrPeriod: 12,
      minimumProfitPercent: 0.2,
      minimumProfitIncrementPercent: 0.075,
      customSessionEnabled: false,
      customSession: "1000-1100",
      debug: false,
    };

    this.cfg = { ...defaultConfig, ...(config || {}) };
    this.emaAlpha = 2 / (this.cfg.emaLength + 1);
  }

  /**
   * Main strategy execution method
   * Processes candles and returns signals
   */
  runStrategy(candles: Candle[]): ORBSignal[] {
    // Reset state for new run
    this.candles = [...candles];
    this.orbList = [];
    this.emaValue = undefined;
    this.atrValue = undefined;
    this.lastClose = undefined;
    this.lastBarTime = undefined;
    this.idx = -1;

    const signals: ORBSignal[] = [];

    // Process each candle sequentially
    for (const candle of candles) {
      const newSignals = this.processCandle(candle);
      signals.push(...newSignals);
    }

    return signals;
  }

  /**
   * Process a single candle and return any generated signals
   */
  private processCandle(candle: Candle): ORBSignal[] {
    this.idx += 1;
    const signals: ORBSignal[] = [];

    // Update EMA
    const mid = (candle.high + candle.low) / 2.0;
    if (this.emaValue === undefined) {
      this.emaValue = mid;
    } else {
      this.emaValue = this.emaValue + (mid - this.emaValue) * this.emaAlpha!;
    }

    // Update ATR (Wilder smoothing)
    const tr = this.lastClose === undefined
      ? candle.high - candle.low
      : Math.max(
          candle.high - candle.low,
          Math.abs(candle.high - this.lastClose),
          Math.abs(candle.low - this.lastClose)
        );
    
    if (this.atrValue === undefined) {
      this.atrValue = tr;
    } else {
      const period = this.cfg.atrPeriod;
      this.atrValue = (this.atrValue * (period - 1) + tr) / period;
    }

    // Session logic
    const isSessionStart = this.isSessionStart(candle);
    const orbTimeMs = this.getOrbMinutes() * 60 * 1000;

    // Handle session start
    if (isSessionStart) {
      const lastORB = this.orbList[0];
      if (lastORB && lastORB.foundEntryTick && lastORB.slIndex === undefined) {
        // Close previous position
        signals.push({
          type: 'EXIT',
          price: this.lastClose ?? candle.close,
          index: this.idx - 1
        });
      }

      // Create new ORB
      const newORB: ORB = {
        h: candle.high,
        l: candle.low,
        startTime: candle.time,
        startIndex: this.idx,
        state: ORBState.OpeningRange,
        breakouts: [],
        foundEntryTick: false,
      };
      this.orbList.unshift(newORB);
    }

    const currentORB = this.orbList[0];
    if (currentORB) {
      const orbSignals = this.processORB(currentORB, candle, orbTimeMs);
      signals.push(...orbSignals);
    }

    // Update state
    this.lastClose = candle.close;
    this.lastBarTime = candle.time;

    return signals;
  }

  /**
   * Process ORB state machine and generate signals
   */
  private processORB(orb: ORB, candle: Candle, orbTimeMs: number): ORBSignal[] {
    const signals: ORBSignal[] = [];

    // Update ORB high/low during opening range
    if (orb.state === ORBState.OpeningRange) {
      if (candle.time < orb.startTime + orbTimeMs) {
        orb.h = Math.max(orb.h, candle.high);
        orb.l = Math.min(orb.l, candle.low);
      } else {
        orb.state = ORBState.WaitingForBreakouts;
      }
    }

    // Wait for breakout
    if (orb.state === ORBState.WaitingForBreakouts) {
      const conditionPrice = this.cfg.breakoutCondition === "EMA" ? this.emaValue! : candle.close;
      
      if (conditionPrice > orb.h || conditionPrice < orb.l) {
        const newBreakout: Breakout = {
          isBullish: conditionPrice > orb.h,
          startIndex: this.idx,
          retests: 0,
        };
        orb.breakouts.unshift(newBreakout);
        orb.state = ORBState.InBreakout;
      }
    }

    // Handle breakouts
    if (orb.state === ORBState.InBreakout) {
      const breakoutSignals = this.processBreakout(orb, candle);
      signals.push(...breakoutSignals);
    }

    // Handle entry taken state
    if (orb.state === ORBState.EntryTaken) {
      const entrySignals = this.processEntryTaken(orb, candle);
      signals.push(...entrySignals);
    }

    return signals;
  }

  /**
   * Get ORB time in minutes
   */
  private getOrbMinutes(): number {
    return parseInt(this.cfg.orbTime, 10);
  }

  /**
   * Check if this candle starts a new session
   */
  private isSessionStart(candle: Candle): boolean {
    if (this.cfg.customSessionEnabled) {
      const session = this.parseSession(this.cfg.customSession);
      const minutes = this.minutesOfDay(candle.time);
      const prevMinutes = this.lastBarTime ? this.minutesOfDay(this.lastBarTime) : undefined;
      const inSession = minutes >= session.startMin && minutes < session.endMin;
      const prevInSession = prevMinutes !== undefined && 
        prevMinutes >= session.startMin && prevMinutes < session.endMin;
      return inSession && !prevInSession;
    } else {
      if (this.lastBarTime === undefined) return true;
      const prevDate = new Date(this.lastBarTime);
      const curDate = new Date(candle.time);
      return curDate.getUTCDate() !== prevDate.getUTCDate() ||
        curDate.getUTCMonth() !== prevDate.getUTCMonth() ||
        curDate.getUTCFullYear() !== prevDate.getUTCFullYear();
    }
  }

  /**
   * Parse custom session string
   */
  private parseSession(session: string): { startMin: number; endMin: number } {
    const [a, b] = session.split("-");
    const parse = (s: string) => {
      const hh = parseInt(s.slice(0, -2), 10);
      const mm = parseInt(s.slice(-2), 10);
      return hh * 60 + mm;
    };
    return { startMin: parse(a), endMin: parse(b) };
  }

  /**
   * Get minutes since midnight
   */
  private minutesOfDay(ms: number): number {
    const d = new Date(ms);
    return d.getHours() * 60 + d.getMinutes();
  }

  /**
   * Process breakout logic and generate entry signals
   */
  private processBreakout(orb: ORB, candle: Candle): ORBSignal[] {
    const signals: ORBSignal[] = [];
    const curBreakout = orb.breakouts[0];

    // Check for failed breakout
    if ((curBreakout.isBullish && candle.close < orb.h) ||
        (!curBreakout.isBullish && candle.close > orb.l)) {
      curBreakout.failed = true;
      curBreakout.endIndex = this.idx;
      orb.state = ORBState.WaitingForBreakouts;
      return signals;
    }

    // Count retests
    if (this.idx > curBreakout.startIndex) {
      if (curBreakout.isBullish && candle.close > orb.h && candle.low < orb.h) {
        curBreakout.retests += 1;
      } else if (!curBreakout.isBullish && candle.close < orb.l && candle.high > orb.l) {
        curBreakout.retests += 1;
      }
    }

    // Check if enough retests for entry
    const retestsNeeded = this.getRetestsNeeded();
    if (curBreakout.retests >= retestsNeeded) {
      curBreakout.endIndex = this.idx;
      orb.state = ORBState.EntryTaken;
      orb.entryATR = this.atrValue!;
      orb.foundEntryTick = true;
      orb.entryIndex = this.idx;
      orb.entryPrice = candle.close;
      orb.entryBullish = curBreakout.isBullish;

      // Set stop loss
      orb.slPrice = this.calculateStopLoss(orb);

      // Generate entry signal
      signals.push({
        type: curBreakout.isBullish ? 'BUY' : 'SELL',
        price: candle.close,
        index: this.idx,
        bullish: curBreakout.isBullish
      });
    }

    return signals;
  }

  /**
   * Process entry taken state for TP/SL signals
   */
  private processEntryTaken(orb: ORB, candle: Candle): ORBSignal[] {
    const signals: ORBSignal[] = [];

    // Process take profit signals
    if (this.cfg.tpMethod === "Dynamic") {
      const tpSignals = this.processDynamicTP(orb, candle);
      signals.push(...tpSignals);
    } else if (this.cfg.tpMethod === "ATR") {
      const tpSignals = this.processATRTP(orb, candle);
      signals.push(...tpSignals);
    }

    // Process stop loss
    if (orb.slPrice !== undefined && orb.slIndex === undefined) {
      const hitSL = (orb.entryBullish && candle.low < orb.slPrice) ||
                   (!orb.entryBullish && candle.high > orb.slPrice);

      if (hitSL) {
        // Check if not same bar as TP
        const hitTP = [orb.tp1Index, orb.tp2Index, orb.tp3Index].includes(this.idx);
        if (!hitTP) {
          orb.slFoundTick = true;
          orb.slIndex = this.idx;
          signals.push({
            type: 'SL',
            price: orb.slPrice!,
            index: this.idx
          });
        }
      }
    }

    return signals;
  }

  /**
   * Get required number of retests based on sensitivity
   */
  private getRetestsNeeded(): number {
    switch (this.cfg.sensitivity) {
      case "High": return 0;
      case "Medium": return 1;
      case "Low": return 2;
      case "Lowest": return 3;
      default: return 1;
    }
  }

  /**
   * Calculate stop loss price
   */
  private calculateStopLoss(orb: ORB): number {
    if (this.cfg.slMethod === "Fixed") {
      const multiplier = orb.entryBullish ?
        (100.0 - this.cfg.stopLossPercent) / 100.0 :
        (100.0 + this.cfg.stopLossPercent) / 100.0;
      return orb.entryPrice! * multiplier;
    } else {
      const center = (orb.h + orb.l) / 2.0;
      switch (this.cfg.slMethod) {
        case "Safer":
          return orb.entryBullish ? (center + orb.h) / 2.0 : (center + orb.l) / 2.0;
        case "Balanced":
          return center;
        case "Risky":
          return orb.entryBullish ? (center + orb.l) / 2.0 : (center + orb.h) / 2.0;
        default:
          return center;
      }
    }
  }

  /**
   * Process dynamic take profit signals
   */
  private processDynamicTP(orb: ORB, candle: Candle): ORBSignal[] {
    const signals: ORBSignal[] = [];
    const isProfitable = (orb.entryBullish && this.emaValue! > orb.entryPrice!) ||
                        (!orb.entryBullish && this.emaValue! < orb.entryPrice!);

    if (!isProfitable) return signals;

    const diffPercent = (Math.abs(this.emaValue! - orb.entryPrice!) / orb.entryPrice!) * 100.0;

    if (diffPercent >= this.cfg.minimumProfitPercent) {
      const crossedEMA = (orb.entryBullish && candle.close < this.emaValue!) ||
                        (!orb.entryBullish && candle.close > this.emaValue!);

      if (crossedEMA) {
        if (orb.tp1Index === undefined) {
          orb.tp1Index = this.idx;
          orb.tp1Price = this.emaValue!;
          orb.tp1FoundTick = true;
          if (this.cfg.adaptiveSL) orb.slPrice = orb.entryPrice!;

          signals.push({
            type: 'TP1',
            price: this.emaValue!,
            index: this.idx,
            level: 1
          });
        } else if (orb.tp2Index === undefined && this.checkTPIncrement(orb.tp1Price!, this.emaValue!, orb.entryBullish!)) {
          orb.tp2Index = this.idx;
          orb.tp2Price = this.emaValue!;
          orb.tp2FoundTick = true;

          signals.push({
            type: 'TP2',
            price: this.emaValue!,
            index: this.idx,
            level: 2
          });
        } else if (orb.tp3Index === undefined && orb.tp2Price && this.checkTPIncrement(orb.tp2Price, this.emaValue!, orb.entryBullish!)) {
          orb.tp3Index = this.idx;
          orb.tp3Price = this.emaValue!;
          orb.tp3FoundTick = true;
          orb.slIndex = -1; // Release stop loss

          signals.push({
            type: 'TP3',
            price: this.emaValue!,
            index: this.idx,
            level: 3
          });
        }
      }
    }

    return signals;
  }

  /**
   * Process ATR-based take profit signals
   */
  private processATRTP(orb: ORB, candle: Candle): ORBSignal[] {
    const signals: ORBSignal[] = [];
    const direction = orb.entryBullish ? 1 : -1;

    const tp1Price = orb.entryPrice! + (orb.entryATR! * this.cfg.atrTP1Mult * direction);
    const tp2Price = orb.entryPrice! + (orb.entryATR! * this.cfg.atrTP2Mult * direction);
    const tp3Price = orb.entryPrice! + (orb.entryATR! * this.cfg.atrTP3Mult * direction);

    // Check TP1
    if (orb.tp1Index === undefined && this.checkATRTP(candle, tp1Price, orb.entryBullish!)) {
      orb.tp1Index = this.idx;
      orb.tp1Price = tp1Price;
      orb.tp1FoundTick = true;
      if (this.cfg.adaptiveSL) orb.slPrice = orb.entryPrice!;

      signals.push({
        type: 'TP1',
        price: tp1Price,
        index: this.idx,
        level: 1
      });
    }
    // Check TP2
    else if (orb.tp2Index === undefined && orb.tp1Index !== undefined &&
             this.checkATRTP(candle, tp2Price, orb.entryBullish!)) {
      orb.tp2Index = this.idx;
      orb.tp2Price = tp2Price;
      orb.tp2FoundTick = true;

      signals.push({
        type: 'TP2',
        price: tp2Price,
        index: this.idx,
        level: 2
      });
    }
    // Check TP3
    else if (orb.tp3Index === undefined && orb.tp2Index !== undefined &&
             this.checkATRTP(candle, tp3Price, orb.entryBullish!)) {
      orb.tp3Index = this.idx;
      orb.tp3Price = tp3Price;
      orb.tp3FoundTick = true;
      orb.slIndex = -1; // Release stop loss

      signals.push({
        type: 'TP3',
        price: tp3Price,
        index: this.idx,
        level: 3
      });
    }

    return signals;
  }

  /**
   * Check if TP increment is sufficient for dynamic TP
   */
  private checkTPIncrement(prevTPPrice: number, currentEMA: number, isBullish: boolean): boolean {
    const diffPercent = (Math.abs(currentEMA - prevTPPrice) / prevTPPrice) * 100.0;
    const correctDirection = (isBullish && currentEMA > prevTPPrice) ||
                           (!isBullish && currentEMA < prevTPPrice);
    return correctDirection && diffPercent >= this.cfg.minimumProfitIncrementPercent;
  }

  /**
   * Check if ATR TP level is hit
   */
  private checkATRTP(candle: Candle, tpPrice: number, isBullish: boolean): boolean {
    return (isBullish && candle.high >= tpPrice) || (!isBullish && candle.low <= tpPrice);
  }
}
