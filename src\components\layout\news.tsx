"use client"

import { motion } from "framer-motion"
import { TrendingUp, TrendingDown, DollarSign, BarChart3 } from "lucide-react"

export function News() {
  const marketNews = [
    {
      title: "NIFTY 50 Hits New All-Time High",
      description: "Indian benchmark index surges 2.3% on strong earnings outlook",
      change: "+2.3%",
      trend: "up",
      time: "2 hours ago"
    },
    {
      title: "Tech Stocks Rally Continues",
      description: "IT sector leads gains with TCS, Infosys showing strong momentum",
      change: "+4.1%",
      trend: "up",
      time: "4 hours ago"
    },
    {
      title: "Banking Sector Mixed",
      description: "Private banks outperform PSU banks amid rate cut expectations",
      change: "-0.8%",
      trend: "down",
      time: "6 hours ago"
    }
  ]

  return (
    <div className="hidden lg:flex lg:flex-col lg:justify-center lg:items-center lg:px-8 lg:h-full lg:min-h-full">
      <motion.div
        className="mx-auto max-w-md w-full"
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.8, delay: 0.3 }}
      >
        <div className="space-y-6">
          <motion.div
            className="flex items-center space-x-3"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <BarChart3 className="h-8 w-8 text-green-400" />
            <h2 className="text-2xl font-bold text-white">Market News</h2>
          </motion.div>

          <div className="space-y-4">
            {marketNews.map((news, index) => (
              <motion.div
                key={index}
                className="bg-gray-800/50 border border-gray-700 rounded-lg p-4 space-y-3"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.7 + index * 0.1 }}
              >
                <div className="flex items-start justify-between">
                  <h3 className="text-sm font-semibold text-white leading-tight">
                    {news.title}
                  </h3>
                  <div className={`flex items-center space-x-1 text-xs font-medium ${
                    news.trend === 'up' ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {news.trend === 'up' ? (
                      <TrendingUp className="h-3 w-3" />
                    ) : (
                      <TrendingDown className="h-3 w-3" />
                    )}
                    <span>{news.change}</span>
                  </div>
                </div>
                
                <p className="text-xs text-gray-400 leading-relaxed">
                  {news.description}
                </p>
                
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">{news.time}</span>
                  <DollarSign className="h-3 w-3 text-gray-500" />
                </div>
              </motion.div>
            ))}
          </div>

          <motion.div
            className="mt-8 space-y-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 1.2 }}
          >
            <div className="flex space-x-1">
              {[...Array(5)].map((_, i) => (
                <motion.div
                  key={i}
                  className="h-2 w-2 rounded-full bg-green-400 opacity-60"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{
                    duration: 0.3,
                    delay: 1.4 + i * 0.1,
                    type: "spring",
                    stiffness: 300
                  }}
                />
              ))}
            </div>
            <p className="text-xs text-gray-500">
              Real-time market updates and trading insights
            </p>
          </motion.div>
        </div>
      </motion.div>
    </div>
  )
}
