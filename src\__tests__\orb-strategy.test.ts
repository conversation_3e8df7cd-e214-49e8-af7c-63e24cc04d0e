/**
 * ORB Strategy Integration Tests
 * Tests the core functionality of the ORB (Opening Range Breakout) strategy
 */

import { ORBStrategy, ORBConfig, Candle, ORBSignal } from '@/lib/strategies/orbStrategy';

describe('ORB Strategy', () => {
  let strategy: ORBStrategy;
  let defaultConfig: ORBConfig;

  beforeEach(() => {
    defaultConfig = {
      orbTime: "30",
      sensitivity: "Medium",
      breakoutCondition: "Close",
      tpMethod: "Dynamic",
      emaLength: 9,
      slMethod: "Balanced",
      adaptiveSL: true,
      stopLossPercent: 1.0,
      atrTP1Mult: 0.75,
      atrTP2Mult: 1.5,
      atrTP3Mult: 2.25,
      atrPeriod: 12,
      minimumProfitPercent: 0.2,
      minimumProfitIncrementPercent: 0.075,
      customSessionEnabled: false,
      customSession: "1000-1100",
      debug: false,
    };
    strategy = new ORBStrategy(defaultConfig);
  });

  describe('Strategy Initialization', () => {
    it('should initialize with default configuration', () => {
      const defaultStrategy = new ORBStrategy();
      expect(defaultStrategy).toBeDefined();
    });

    it('should initialize with custom configuration', () => {
      const customConfig: ORBConfig = {
        orbTime: "60",
        sensitivity: "High",
        breakoutCondition: "EMA",
        tpMethod: "ATR"
      };
      const customStrategy = new ORBStrategy(customConfig);
      expect(customStrategy).toBeDefined();
    });
  });

  describe('Signal Generation', () => {
    it('should generate no signals with insufficient data', () => {
      const candles: Candle[] = [
        { time: Date.now(), open: 100, high: 102, low: 98, close: 101 }
      ];
      
      const signals = strategy.runStrategy(candles);
      expect(signals).toHaveLength(0);
    });

    it('should handle opening range formation', () => {
      const baseTime = new Date('2024-01-01T10:00:00Z').getTime();
      const candles: Candle[] = [];
      
      // Create 30-minute opening range (6 candles of 5 minutes each)
      for (let i = 0; i < 6; i++) {
        candles.push({
          time: baseTime + (i * 5 * 60 * 1000),
          open: 100 + i,
          high: 105 + i,
          low: 95 + i,
          close: 102 + i
        });
      }
      
      const signals = strategy.runStrategy(candles);
      // Should not generate signals during opening range formation
      expect(signals.filter(s => s.type === 'BUY' || s.type === 'SELL')).toHaveLength(0);
    });

    it('should generate bullish breakout signal', () => {
      const baseTime = new Date('2024-01-01T10:00:00Z').getTime();
      const candles: Candle[] = [];
      
      // Opening range candles (30 minutes)
      for (let i = 0; i < 6; i++) {
        candles.push({
          time: baseTime + (i * 5 * 60 * 1000),
          open: 100,
          high: 105, // ORB high
          low: 95,   // ORB low
          close: 102
        });
      }
      
      // Breakout candle above ORB high
      candles.push({
        time: baseTime + (6 * 5 * 60 * 1000),
        open: 105,
        high: 110,
        low: 104,
        close: 108 // Close above ORB high (105)
      });
      
      // Retest candle (for Medium sensitivity)
      candles.push({
        time: baseTime + (7 * 5 * 60 * 1000),
        open: 108,
        high: 109,
        low: 104, // Low touches ORB high for retest
        close: 107
      });
      
      const signals = strategy.runStrategy(candles);
      const buySignals = signals.filter(s => s.type === 'BUY');
      expect(buySignals.length).toBeGreaterThan(0);
      expect(buySignals[0].bullish).toBe(true);
    });

    it('should generate bearish breakout signal', () => {
      const baseTime = new Date('2024-01-01T10:00:00Z').getTime();
      const candles: Candle[] = [];
      
      // Opening range candles
      for (let i = 0; i < 6; i++) {
        candles.push({
          time: baseTime + (i * 5 * 60 * 1000),
          open: 100,
          high: 105,
          low: 95,
          close: 102
        });
      }
      
      // Breakout candle below ORB low
      candles.push({
        time: baseTime + (6 * 5 * 60 * 1000),
        open: 95,
        high: 96,
        low: 90,
        close: 92 // Close below ORB low (95)
      });
      
      // Retest candle
      candles.push({
        time: baseTime + (7 * 5 * 60 * 1000),
        open: 92,
        high: 96, // High touches ORB low for retest
        low: 91,
        close: 93
      });
      
      const signals = strategy.runStrategy(candles);
      const sellSignals = signals.filter(s => s.type === 'SELL');
      expect(sellSignals.length).toBeGreaterThan(0);
      expect(sellSignals[0].bullish).toBe(false);
    });
  });

  describe('Sensitivity Settings', () => {
    it('should require no retests for High sensitivity', () => {
      const highSensitivityConfig = { ...defaultConfig, sensitivity: "High" as const };
      const highStrategy = new ORBStrategy(highSensitivityConfig);
      
      const baseTime = new Date('2024-01-01T10:00:00Z').getTime();
      const candles: Candle[] = [];
      
      // Opening range
      for (let i = 0; i < 6; i++) {
        candles.push({
          time: baseTime + (i * 5 * 60 * 1000),
          open: 100,
          high: 105,
          low: 95,
          close: 102
        });
      }
      
      // Immediate breakout without retest
      candles.push({
        time: baseTime + (6 * 5 * 60 * 1000),
        open: 105,
        high: 110,
        low: 104,
        close: 108
      });
      
      const signals = highStrategy.runStrategy(candles);
      const buySignals = signals.filter(s => s.type === 'BUY');
      expect(buySignals.length).toBeGreaterThan(0);
    });

    it('should require more retests for Lowest sensitivity', () => {
      const lowestSensitivityConfig = { ...defaultConfig, sensitivity: "Lowest" as const };
      const lowestStrategy = new ORBStrategy(lowestSensitivityConfig);
      
      const baseTime = new Date('2024-01-01T10:00:00Z').getTime();
      const candles: Candle[] = [];
      
      // Opening range
      for (let i = 0; i < 6; i++) {
        candles.push({
          time: baseTime + (i * 5 * 60 * 1000),
          open: 100,
          high: 105,
          low: 95,
          close: 102
        });
      }
      
      // Breakout with only one retest (should not trigger for Lowest sensitivity)
      candles.push({
        time: baseTime + (6 * 5 * 60 * 1000),
        open: 105,
        high: 110,
        low: 104,
        close: 108
      });
      
      candles.push({
        time: baseTime + (7 * 5 * 60 * 1000),
        open: 108,
        high: 109,
        low: 104,
        close: 107
      });
      
      const signals = lowestStrategy.runStrategy(candles);
      const buySignals = signals.filter(s => s.type === 'BUY');
      expect(buySignals).toHaveLength(0); // Should not trigger with only 1 retest
    });
  });

  describe('Take Profit Methods', () => {
    it('should handle ATR-based take profit', () => {
      const atrConfig = { ...defaultConfig, tpMethod: "ATR" as const };
      const atrStrategy = new ORBStrategy(atrConfig);
      
      const baseTime = new Date('2024-01-01T10:00:00Z').getTime();
      const candles: Candle[] = [];
      
      // Create scenario with entry and TP levels
      // This is a simplified test - in reality, you'd need more complex price action
      for (let i = 0; i < 20; i++) {
        candles.push({
          time: baseTime + (i * 5 * 60 * 1000),
          open: 100 + i * 0.5,
          high: 105 + i * 0.5,
          low: 95 + i * 0.5,
          close: 102 + i * 0.5
        });
      }
      
      const signals = atrStrategy.runStrategy(candles);
      // This test verifies the strategy runs without errors with ATR method
      expect(signals).toBeDefined();
    });
  });

  describe('Custom Session Handling', () => {
    it('should handle custom session times', () => {
      const customSessionConfig = {
        ...defaultConfig,
        customSessionEnabled: true,
        customSession: "0930-1000"
      };
      const customStrategy = new ORBStrategy(customSessionConfig);
      
      // Create candles around custom session time
      const sessionStart = new Date('2024-01-01T09:30:00Z').getTime();
      const candles: Candle[] = [];
      
      for (let i = 0; i < 10; i++) {
        candles.push({
          time: sessionStart + (i * 5 * 60 * 1000),
          open: 100,
          high: 105,
          low: 95,
          close: 102
        });
      }
      
      const signals = customStrategy.runStrategy(candles);
      expect(signals).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle empty candle array', () => {
      const signals = strategy.runStrategy([]);
      expect(signals).toHaveLength(0);
    });

    it('should handle invalid candle data', () => {
      const invalidCandles: Candle[] = [
        { time: NaN, open: 100, high: 105, low: 95, close: 102 }
      ];

      expect(() => strategy.runStrategy(invalidCandles)).not.toThrow();
    });
  });

  describe('Stop Loss Methods', () => {
    it('should calculate fixed stop loss correctly', () => {
      const fixedSLConfig = {
        ...defaultConfig,
        slMethod: "Fixed" as const,
        stopLossPercent: 2.0
      };
      const fixedSLStrategy = new ORBStrategy(fixedSLConfig);

      const baseTime = new Date('2024-01-01T10:00:00Z').getTime();
      const candles: Candle[] = [];

      // Create opening range and breakout scenario
      for (let i = 0; i < 6; i++) {
        candles.push({
          time: baseTime + (i * 5 * 60 * 1000),
          open: 100,
          high: 105,
          low: 95,
          close: 102
        });
      }

      // Breakout and retest
      candles.push({
        time: baseTime + (6 * 5 * 60 * 1000),
        open: 105,
        high: 110,
        low: 104,
        close: 108
      });

      candles.push({
        time: baseTime + (7 * 5 * 60 * 1000),
        open: 108,
        high: 109,
        low: 104,
        close: 107
      });

      const signals = fixedSLStrategy.runStrategy(candles);
      expect(signals).toBeDefined();
    });
  });

  describe('Integration with Market Data', () => {
    it('should process realistic market data sequence', () => {
      const baseTime = new Date('2024-01-01T09:15:00Z').getTime();
      const candles: Candle[] = [];

      // Simulate realistic intraday price action
      const prices = [
        { o: 18500, h: 18520, l: 18480, c: 18510 },
        { o: 18510, h: 18530, l: 18490, c: 18525 },
        { o: 18525, h: 18540, l: 18500, c: 18535 },
        { o: 18535, h: 18550, l: 18520, c: 18545 },
        { o: 18545, h: 18560, l: 18530, c: 18555 },
        { o: 18555, h: 18570, l: 18540, c: 18565 },
        // Breakout above range
        { o: 18565, h: 18590, l: 18560, c: 18580 },
        { o: 18580, h: 18595, l: 18565, c: 18575 }, // Retest
        { o: 18575, h: 18600, l: 18570, c: 18590 },
        { o: 18590, h: 18610, l: 18585, c: 18605 }
      ];

      prices.forEach((price, i) => {
        candles.push({
          time: baseTime + (i * 5 * 60 * 1000),
          open: price.o,
          high: price.h,
          low: price.l,
          close: price.c,
          volume: 1000 + i * 100
        });
      });

      const signals = strategy.runStrategy(candles);
      expect(signals).toBeDefined();
      expect(Array.isArray(signals)).toBe(true);
    });
  });
});
