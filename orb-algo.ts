/**
 * ORB (Opening Range Breakout) Strategy Implementation
 * Adapted for integration with the existing trading platform
 * Original algorithm converted from Pine Script to TypeScript
 */

export type Bar = {
  time: number; // ms
  open: number;
  high: number;
  low: number;
  close: number;
  volume?: number;
};

export type Signal =
  | { type: "entry"; bullish: boolean; price: number; index: number }
  | { type: "tp"; level: 1 | 2 | 3; price: number; index: number }
  | { type: "sl"; price: number; index: number }
  | { type: "exit"; price: number; index: number };

export type ORBConfig = {
  orbTime: "15" | "30" | "45" | "60" | "90" | "120" | "180" | "240"; // minutes (string just like Pine conversion)
  sensitivity?: "High" | "Medium" | "Low" | "Lowest";
  breakoutCondition?: "Close" | "EMA";
  tpMethod?: "Dynamic" | "ATR";
  emaLength?: number; // used for EMA
  slMethod?: "Safer" | "Balanced" | "Risky" | "Fixed";
  adaptiveSL?: boolean;
  stopLossPercent?: number; // used only when slMethod == "Fixed"
  atrTP1Mult?: number;
  atrTP2Mult?: number;
  atrTP3Mult?: number;
  atrPeriod?: number;
  minimumProfitPercent?: number; // dynamic TP thresholds in percent
  minimumProfitIncrementPercent?: number;
  customSessionEnabled?: boolean;
  customSession?: string; // format "HHMM-HHMM", e.g. "0930-1000"
  showHistoricZones?: boolean; // no plotting in TS but kept for parity
  debug?: boolean;
};

enum ORBState {
  OpeningRange = "Opening Range",
  WaitingForBreakouts = "Waiting For Breakouts",
  InBreakout = "In Breakout",
  EntryTaken = "Entry Taken",
}

type Breakout = {
  isBullish: boolean;
  startIndex: number;
  endIndex?: number;
  failed?: boolean;
  retests: number;
};

type ORB = {
  h: number;
  l: number;
  startTime: number; // ms
  startIndex: number;
  endIndex?: number;
  isLastSession?: boolean;

  state: ORBState;
  breakouts: Breakout[];

  foundEntryTick: boolean;
  entryBullish?: boolean;
  entryIndex?: number;
  entryPrice?: number;
  entryATR?: number;

  tp1Index?: number;
  tp1Price?: number;
  tp1FoundTick?: boolean;

  tp2Index?: number;
  tp2Price?: number;
  tp2FoundTick?: boolean;

  tp3Index?: number;
  tp3Price?: number;
  tp3FoundTick?: boolean;

  slIndex?: number;
  slPrice?: number;
  slFoundTick?: boolean;

  isExitTrade?: boolean;
  exitTradeFoundTick?: boolean;
};

export class ORBAlgo {
  cfg: Required<ORBConfig>;

  // runtime
  bars: Bar[] = [];
  orbList: ORB[] = [];

  // indicators / state for EMA and ATR
  emaValue?: number;
  emaAlpha?: number;
  atrValue?: number; // Wilder ATR smoothing
  lastClose?: number;
  lastBarTime?: number;

  // index counter
  idx = -1;

  // signals emitted
  public signals: Signal[] = [];

  constructor(cfg?: ORBConfig) {
    const defaultCfg: Required<Pick<ORBConfig,
      | "sensitivity" | "breakoutCondition" | "tpMethod" | "emaLength" | "slMethod" | "adaptiveSL"
      | "stopLossPercent" | "atrTP1Mult" | "atrTP2Mult" | "atrTP3Mult" | "atrPeriod"
      | "minimumProfitPercent" | "minimumProfitIncrementPercent" | "customSessionEnabled" | "customSession"
      | "showHistoricZones" | "debug">> & { orbTime: ORBConfig["orbTime"]; atrPeriod: number } = {
      orbTime: "30",
      sensitivity: "Medium",
      breakoutCondition: "Close",
      tpMethod: "Dynamic",
      emaLength: 9,
      slMethod: "Balanced",
      adaptiveSL: true,
      stopLossPercent: 1.0,
      atrTP1Mult: 0.75,
      atrTP2Mult: 1.5,
      atrTP3Mult: 2.25,
      atrPeriod: 12,
      minimumProfitPercent: 0.2,
      minimumProfitIncrementPercent: 0.075,
      customSessionEnabled: false,
      customSession: "1000-1100",
      showHistoricZones: true,
      debug: false,
    };

    this.cfg = { ...defaultCfg, ...(cfg || {}) } as Required<ORBConfig>;
    this.emaAlpha = 2 / (this.cfg.emaLength + 1);
  }

  // helper: parse custom session "HHMM-HHMM" into minute offsets in day
  private parseSession(session: string): { startMin: number; endMin: number } {
    const [a, b] = session.split("-");
    const parse = (s: string) => {
      const hh = parseInt(s.slice(0, -2), 10);
      const mm = parseInt(s.slice(-2), 10);
      return hh * 60 + mm;
    };
    return { startMin: parse(a), endMin: parse(b) };
  }

  // returns minutes since midnight for ms timestamp (local time of timestamp)
  private minutesOfDay(ms: number): number {
    const d = new Date(ms);
    return d.getHours() * 60 + d.getMinutes();
  }

  private getOrbMinutes(): number {
    return parseInt(this.cfg.orbTime, 10);
  }

  // call this for each sequential bar
  public processBar(bar: Bar): Signal[] {
    this.idx += 1;
    this.bars.push(bar);

    // update EMA
    const mid = (bar.high + bar.low) / 2.0;
    if (this.emaValue === undefined) {
      // initialize EMA with mid (or first close)
      this.emaValue = mid;
    } else {
      this.emaValue = this.emaValue + (mid - this.emaValue) * (this.emaAlpha ?? (2 / (this.cfg.emaLength + 1)));
    }

    // update ATR (Wilder smoothing)
    const tr = this.lastClose === undefined
      ? bar.high - bar.low
      : Math.max(bar.high - bar.low, Math.abs(bar.high - this.lastClose), Math.abs(bar.low - this.lastClose));
    if (this.atrValue === undefined) {
      // simple average of first atrPeriod TRs could be used, but initialize with current TR for streaming
      this.atrValue = tr;
    } else {
      const period = this.cfg.atrPeriod;
      this.atrValue = (this.atrValue * (period - 1) + tr) / period;
    }

    // session logic: determine if this bar is first bar of session
    const isSessionStart = this.isSessionStart(bar);
    const orbTimeMs = this.getOrbMinutes() * 60 * 1000;

    // At confirmed bar end (we assume the caller feeds closed bars sequentially)
    // create new ORB on session start
    if (isSessionStart) {
      // end previous ORB if exists
      const lastORB = this.orbList[0];
      if (lastORB) {
        lastORB.endIndex = this.idx - 1;
        // if there was entry and no SL, close at last close (simulate exit)
        if (lastORB.foundEntryTick && lastORB.slIndex === undefined) {
          lastORB.slPrice = this.lastClose ?? bar.close;
          lastORB.slIndex = this.idx - 1;
          lastORB.exitTradeFoundTick = true;
          this.signals.push({ type: "exit", price: lastORB.slPrice!, index: lastORB.slIndex! });
        }
      }
      // create new ORB with current bar as start
      const newORB: ORB = {
        h: bar.high,
        l: bar.low,
        startTime: bar.time,
        startIndex: this.idx,
        state: ORBState.OpeningRange,
        breakouts: [],
        foundEntryTick: false,
      };
      this.orbList.unshift(newORB);
    }

    const lastORB = this.orbList[0];

    // update lastBarTime and lastClose after using them for calculations
    // (we need lastClose for next bar TR; but Pine used close[1] earlier - logic approximated)
    // Update ORB high/low during OpeningRange
    if (lastORB) {
      if (lastORB.state === ORBState.OpeningRange) {
        // Opening range window: startTime + orbTimeMs
        if (bar.time < lastORB.startTime + orbTimeMs) {
          lastORB.h = Math.max(isFinite(lastORB.h) ? lastORB.h : -Infinity, bar.high);
          lastORB.l = Math.min(isFinite(lastORB.l) ? lastORB.l : Infinity, bar.low);
        } else {
          lastORB.state = ORBState.WaitingForBreakouts;
        }
      }
      // Wait for breakout
      if (lastORB.state === ORBState.WaitingForBreakouts) {
        const conditionPrice = this.cfg.breakoutCondition === "EMA" ? this.emaValue! : bar.close;
        if (conditionPrice > lastORB.h || conditionPrice < lastORB.l) {
          const newBreakout: Breakout = {
            isBullish: conditionPrice > lastORB.h,
            startIndex: this.idx,
            retests: 0,
          };
          lastORB.breakouts.unshift(newBreakout);
          lastORB.state = ORBState.InBreakout;
        }
      }

      // Handle breakouts
      if (lastORB.state === ORBState.InBreakout) {
        const curBreakout = lastORB.breakouts[0];

        // failed breakout
        if ((curBreakout.isBullish && bar.close < lastORB.h) || (!curBreakout.isBullish && bar.close > lastORB.l)) {
          curBreakout.failed = true;
          curBreakout.endIndex = this.idx;
          lastORB.state = ORBState.WaitingForBreakouts;
        }

        // breakout retest: needs a low/high crossing of breakout price after breakout
        if (this.idx > curBreakout.startIndex) {
          if (curBreakout.isBullish && bar.close > lastORB.h && bar.low < lastORB.h) {
            curBreakout.retests += 1;
          } else if (!curBreakout.isBullish && bar.close < lastORB.l && bar.high > lastORB.l) {
            curBreakout.retests += 1;
          }
        }

        const retestsNeeded = this.cfg.sensitivity === "High" ? 0 :
          this.cfg.sensitivity === "Medium" ? 1 : this.cfg.sensitivity === "Low" ? 2 : 3;

        if (curBreakout.retests >= retestsNeeded) {
          curBreakout.endIndex = this.idx;
          lastORB.state = ORBState.EntryTaken;
          lastORB.entryATR = this.atrValue!;
          lastORB.foundEntryTick = true;
          lastORB.entryIndex = this.idx;
          lastORB.entryPrice = bar.close;
          lastORB.entryBullish = curBreakout.isBullish;

          // stop loss setup
          if (this.cfg.slMethod === "Fixed") {
            if (curBreakout.isBullish) {
              lastORB.slPrice = lastORB.entryPrice! * ((100.0 - this.cfg.stopLossPercent) / 100.0);
            } else {
              lastORB.slPrice = lastORB.entryPrice! * ((100.0 + this.cfg.stopLossPercent) / 100.0);
            }
          } else {
            const center = (lastORB.h + lastORB.l) / 2.0;
            if (this.cfg.slMethod === "Safer") {
              lastORB.slPrice = lastORB.entryBullish ? ((center + lastORB.h) / 2.0) : ((center + lastORB.l) / 2.0);
            } else if (this.cfg.slMethod === "Balanced") {
              lastORB.slPrice = center;
            } else if (this.cfg.slMethod === "Risky") {
              lastORB.slPrice = lastORB.entryBullish ? ((center + lastORB.l) / 2.0) : ((center + lastORB.h) / 2.0);
            }
          }

          // Emit entry signal
          this.signals.push({
            type: "entry",
            bullish: lastORB.entryBullish!,
            price: lastORB.entryPrice!,
            index: lastORB.entryIndex!,
          });
        }
      }

      // Handle EntryTaken -> handle TP/SL conditions
      if (lastORB.state === ORBState.EntryTaken) {
        const isProfitable = ((lastORB.entryBullish && (this.emaValue! > lastORB.entryPrice!)) ||
          (!lastORB.entryBullish && (this.emaValue! < lastORB.entryPrice!)));

        if (this.cfg.tpMethod === "Dynamic") {
          // check TP1: needs isProfitable and EMA distance >= minimumProfitPercent
          const diffPercent = (Math.abs(this.emaValue! - lastORB.entryPrice!) / lastORB.entryPrice!) * 100.0;
          if (isProfitable && diffPercent >= this.cfg.minimumProfitPercent) {
            // TP1: first time crossing of EMA against entry
            if (lastORB.tp1Index === undefined && ((lastORB.entryBullish && bar.close < this.emaValue!) || (!lastORB.entryBullish && bar.close > this.emaValue!))) {
              lastORB.tp1Index = this.idx;
              lastORB.tp1Price = this.emaValue!;
              lastORB.tp1FoundTick = true;
              if (this.cfg.adaptiveSL) lastORB.slPrice = lastORB.entryPrice!;
              this.signals.push({ type: "tp", level: 1, price: lastORB.tp1Price!, index: lastORB.tp1Index! });
            } else if (lastORB.tp1Index !== undefined && lastORB.tp2Index === undefined && ((lastORB.entryBullish && bar.close < this.emaValue!) || (!lastORB.entryBullish && bar.close > this.emaValue!))) {
              // TP2 requires EMA further from tp1 by minimumProfitIncrementPercent
              const diffTP1 = (Math.abs(this.emaValue! - lastORB.tp1Price!) / (lastORB.tp1Price!)) * 100.0;
              if (((lastORB.entryBullish && this.emaValue! > lastORB.tp1Price!) || (!lastORB.entryBullish && this.emaValue! < lastORB.tp1Price!)) &&
                diffTP1 >= this.cfg.minimumProfitIncrementPercent) {
                lastORB.tp2Index = this.idx;
                lastORB.tp2Price = this.emaValue!;
                lastORB.tp2FoundTick = true;
                this.signals.push({ type: "tp", level: 2, price: lastORB.tp2Price!, index: lastORB.tp2Index! });
              }
            } else if (lastORB.tp2Index !== undefined && lastORB.tp3Index === undefined && ((lastORB.entryBullish && bar.close < this.emaValue!) || (!lastORB.entryBullish && bar.close > this.emaValue!))) {
              const diffTP2 = (Math.abs(this.emaValue! - lastORB.tp2Price!) / (lastORB.tp2Price!)) * 100.0;
              if (((lastORB.entryBullish && this.emaValue! > lastORB.tp2Price!) || (!lastORB.entryBullish && this.emaValue! < lastORB.tp2Price!)) &&
                diffTP2 >= this.cfg.minimumProfitIncrementPercent) {
                lastORB.tp3Index = this.idx;
                lastORB.tp3Price = this.emaValue!;
                lastORB.tp3FoundTick = true;
                lastORB.slIndex = -1; // release stoploss as in pine
                this.signals.push({ type: "tp", level: 3, price: lastORB.tp3Price!, index: lastORB.tp3Index! });
              }
            }
          }
        } else if (this.cfg.tpMethod === "ATR") {
          const direction = lastORB.entryBullish ? 1 : -1;
          const tp1Price = lastORB.entryPrice! + (lastORB.entryATR! * this.cfg.atrTP1Mult * direction);
          const tp2Price = lastORB.entryPrice! + (lastORB.entryATR! * this.cfg.atrTP2Mult * direction);
          const tp3Price = lastORB.entryPrice! + (lastORB.entryATR! * this.cfg.atrTP3Mult * direction);

          if (lastORB.tp1Index === undefined && ((lastORB.entryBullish && bar.high >= tp1Price) || (!lastORB.entryBullish && bar.low <= tp1Price))) {
            lastORB.tp1Index = this.idx;
            lastORB.tp1Price = tp1Price;
            lastORB.tp1FoundTick = true;
            if (this.cfg.adaptiveSL) lastORB.slPrice = lastORB.entryPrice!;
            this.signals.push({ type: "tp", level: 1, price: tp1Price, index: this.idx });
          } else if (lastORB.tp2Index === undefined && lastORB.tp1Index !== undefined && ((lastORB.entryBullish && bar.high >= tp2Price) || (!lastORB.entryBullish && bar.low <= tp2Price))) {
            lastORB.tp2Index = this.idx;
            lastORB.tp2Price = tp2Price;
            lastORB.tp2FoundTick = true;
            this.signals.push({ type: "tp", level: 2, price: tp2Price, index: this.idx });
          } else if (lastORB.tp3Index === undefined && lastORB.tp2Index !== undefined && ((lastORB.entryBullish && bar.high >= tp3Price) || (!lastORB.entryBullish && bar.low <= tp3Price))) {
            lastORB.tp3Index = this.idx;
            lastORB.tp3Price = tp3Price;
            lastORB.tp3FoundTick = true;
            lastORB.slIndex = -1;
            this.signals.push({ type: "tp", level: 3, price: tp3Price, index: this.idx });
          }
        }

        // Stop Loss: if price breaches slPrice
        if (lastORB.slPrice !== undefined && lastORB.slIndex === undefined) {
          if ((lastORB.entryBullish && bar.low < lastORB.slPrice) || (!lastORB.entryBullish && bar.high > lastORB.slPrice)) {
            // avoid counting if same bar is TP
            const hitTP = [lastORB.tp1Index, lastORB.tp2Index, lastORB.tp3Index].includes(this.idx);
            if (!hitTP) {
              lastORB.slFoundTick = true;
              lastORB.slIndex = this.idx;
              this.signals.push({ type: "sl", price: lastORB.slPrice!, index: lastORB.slIndex! });
            }
          }
        }
      }
    }

    // housekeeping
    this.lastClose = bar.close;
    this.lastBarTime = bar.time;

    // optionally limit memory by trimming old ORBs (not deleting important ones)
    if (this.orbList.length > 100) this.orbList.splice(100);

    // return newly generated signals for this bar in order
    // Note: this.signals accumulates; return only newly added signals this call
    // We'll return and keep the internal array (incremental)
    const out = this.signals.splice(0); // drain signals to return them
    return out;
  }

  private isSessionStart(bar: Bar): boolean {
    // If custom session enabled, detect first bar of custom session
    if (this.cfg.customSessionEnabled) {
      const session = this.parseSession(this.cfg.customSession);
      const minutes = this.minutesOfDay(bar.time);
      // detect first bar when minutes is within session start minute and previous bar was outside or undefined
      const prevMinutes = this.lastBarTime ? this.minutesOfDay(this.lastBarTime) : undefined;
      const inSession = minutes >= session.startMin && minutes < session.endMin;
      const prevInSession = prevMinutes !== undefined && prevMinutes >= session.startMin && prevMinutes < session.endMin;
      return inSession && !prevInSession;
    } else {
      // Default: treat midnight-based daily session. We'll detect start when day changes (00:00) or when previous bar is undefined.
      if (this.lastBarTime === undefined) return true; // first ever bar = session start
      const prevDate = new Date(this.lastBarTime);
      const curDate = new Date(bar.time);
      // session start if day rolled over
      if (curDate.getUTCDate() !== prevDate.getUTCDate() || curDate.getUTCMonth() !== prevDate.getUTCMonth() || curDate.getUTCFullYear() !== prevDate.getUTCFullYear()) {
        return true;
      }
      // Also allow explicit market-open-like times: if you want to use 09:15 open, enable customSession
      return false;
    }
  }
}
