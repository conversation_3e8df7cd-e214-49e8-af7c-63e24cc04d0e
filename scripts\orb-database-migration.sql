-- ORB Strategy Database Migration
-- This script adds support for ORB strategy in the existing trading platform

-- Update trading_configuration table to support ORB strategy parameters
ALTER TABLE trading_configuration 
ADD COLUMN IF NOT EXISTS orb_time VARCHAR(3) DEFAULT '30',
ADD COLUMN IF NOT EXISTS sensitivity VARCHAR(10) DEFAULT 'Medium',
ADD COLUMN IF NOT EXISTS breakout_condition VARCHAR(10) DEFAULT 'Close',
ADD COLUMN IF NOT EXISTS tp_method VARCHAR(10) DEFAULT 'Dynamic',
ADD COLUMN IF NOT EXISTS ema_length_orb INTEGER DEFAULT 9,
ADD COLUMN IF NOT EXISTS sl_method VARCHAR(10) DEFAULT 'Balanced',
ADD COLUMN IF NOT EXISTS adaptive_sl BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS stop_loss_percent DECIMAL(5,2) DEFAULT 1.0,
ADD COLUMN IF NOT EXISTS atr_tp1_mult DECIMAL(5,2) DEFAULT 0.75,
ADD COLUMN IF NOT EXISTS atr_tp2_mult DECIMAL(5,2) DEFAULT 1.5,
ADD COLUMN IF NOT EXISTS atr_tp3_mult DECIMAL(5,2) DEFAULT 2.25,
ADD COLUMN IF NOT EXISTS atr_period INTEGER DEFAULT 12,
ADD COLUMN IF NOT EXISTS minimum_profit_percent DECIMAL(5,2) DEFAULT 0.2,
ADD COLUMN IF NOT EXISTS minimum_profit_increment_percent DECIMAL(5,2) DEFAULT 0.075,
ADD COLUMN IF NOT EXISTS custom_session_enabled BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS custom_session VARCHAR(11) DEFAULT '1000-1100';

-- Update trading_signals table to support ORB-specific fields
ALTER TABLE trading_signals 
ADD COLUMN IF NOT EXISTS signal_subtype VARCHAR(10), -- 'ENTRY', 'TP1', 'TP2', 'TP3', 'SL', 'EXIT'
ADD COLUMN IF NOT EXISTS tp_level INTEGER, -- 1, 2, or 3 for take profit levels
ADD COLUMN IF NOT EXISTS is_bullish BOOLEAN, -- true for bullish, false for bearish
ADD COLUMN IF NOT EXISTS orb_high DECIMAL(10,2), -- Opening range high
ADD COLUMN IF NOT EXISTS orb_low DECIMAL(10,2), -- Opening range low
ADD COLUMN IF NOT EXISTS entry_atr DECIMAL(10,4), -- ATR at entry time
ADD COLUMN IF NOT EXISTS breakout_retests INTEGER DEFAULT 0; -- Number of retests before entry

-- Create ORB sessions table to track opening range sessions
CREATE TABLE IF NOT EXISTS orb_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    instrument_symbol VARCHAR(50) NOT NULL,
    session_date DATE NOT NULL,
    session_start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    session_end_time TIMESTAMP WITH TIME ZONE,
    orb_high DECIMAL(10,2) NOT NULL,
    orb_low DECIMAL(10,2) NOT NULL,
    orb_time_minutes INTEGER NOT NULL,
    breakouts_count INTEGER DEFAULT 0,
    successful_entries INTEGER DEFAULT 0,
    total_pnl DECIMAL(12,2) DEFAULT 0.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for efficient querying
CREATE INDEX IF NOT EXISTS idx_orb_sessions_user_symbol_date 
ON orb_sessions(user_id, instrument_symbol, session_date);

-- Create ORB breakouts table to track breakout attempts
CREATE TABLE IF NOT EXISTS orb_breakouts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    orb_session_id UUID NOT NULL REFERENCES orb_sessions(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    breakout_time TIMESTAMP WITH TIME ZONE NOT NULL,
    is_bullish BOOLEAN NOT NULL,
    breakout_price DECIMAL(10,2) NOT NULL,
    retests_count INTEGER DEFAULT 0,
    entry_taken BOOLEAN DEFAULT false,
    entry_price DECIMAL(10,2),
    entry_time TIMESTAMP WITH TIME ZONE,
    stop_loss_price DECIMAL(10,2),
    take_profit_1_price DECIMAL(10,2),
    take_profit_2_price DECIMAL(10,2),
    take_profit_3_price DECIMAL(10,2),
    exit_price DECIMAL(10,2),
    exit_time TIMESTAMP WITH TIME ZONE,
    exit_reason VARCHAR(20), -- 'TP1', 'TP2', 'TP3', 'SL', 'MANUAL', 'SESSION_END'
    pnl DECIMAL(12,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for efficient querying
CREATE INDEX IF NOT EXISTS idx_orb_breakouts_session_user 
ON orb_breakouts(orb_session_id, user_id);

-- Update strategy_performance table to include ORB-specific metrics
ALTER TABLE strategy_performance 
ADD COLUMN IF NOT EXISTS orb_sessions_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS orb_breakouts_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS orb_successful_entries INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS orb_avg_retests DECIMAL(5,2) DEFAULT 0.0,
ADD COLUMN IF NOT EXISTS orb_tp1_hit_rate DECIMAL(5,2) DEFAULT 0.0,
ADD COLUMN IF NOT EXISTS orb_tp2_hit_rate DECIMAL(5,2) DEFAULT 0.0,
ADD COLUMN IF NOT EXISTS orb_tp3_hit_rate DECIMAL(5,2) DEFAULT 0.0,
ADD COLUMN IF NOT EXISTS orb_sl_hit_rate DECIMAL(5,2) DEFAULT 0.0;

-- Create function to update ORB session statistics
CREATE OR REPLACE FUNCTION update_orb_session_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- Update the ORB session with latest breakout data
    UPDATE orb_sessions 
    SET 
        breakouts_count = (
            SELECT COUNT(*) 
            FROM orb_breakouts 
            WHERE orb_session_id = NEW.orb_session_id
        ),
        successful_entries = (
            SELECT COUNT(*) 
            FROM orb_breakouts 
            WHERE orb_session_id = NEW.orb_session_id AND entry_taken = true
        ),
        total_pnl = (
            SELECT COALESCE(SUM(pnl), 0) 
            FROM orb_breakouts 
            WHERE orb_session_id = NEW.orb_session_id AND pnl IS NOT NULL
        ),
        updated_at = NOW()
    WHERE id = NEW.orb_session_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update ORB session stats
DROP TRIGGER IF EXISTS trigger_update_orb_session_stats ON orb_breakouts;
CREATE TRIGGER trigger_update_orb_session_stats
    AFTER INSERT OR UPDATE ON orb_breakouts
    FOR EACH ROW
    EXECUTE FUNCTION update_orb_session_stats();

-- Create function to calculate ORB strategy performance metrics
CREATE OR REPLACE FUNCTION calculate_orb_performance(p_user_id UUID, p_instrument_symbol VARCHAR)
RETURNS TABLE (
    total_sessions INTEGER,
    total_breakouts INTEGER,
    successful_entries INTEGER,
    avg_retests DECIMAL,
    tp1_hit_rate DECIMAL,
    tp2_hit_rate DECIMAL,
    tp3_hit_rate DECIMAL,
    sl_hit_rate DECIMAL,
    total_pnl DECIMAL,
    avg_pnl_per_trade DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(DISTINCT os.id)::INTEGER as total_sessions,
        COUNT(ob.id)::INTEGER as total_breakouts,
        COUNT(CASE WHEN ob.entry_taken THEN 1 END)::INTEGER as successful_entries,
        COALESCE(AVG(ob.retests_count), 0)::DECIMAL as avg_retests,
        COALESCE(
            COUNT(CASE WHEN ob.exit_reason = 'TP1' THEN 1 END)::DECIMAL / 
            NULLIF(COUNT(CASE WHEN ob.entry_taken THEN 1 END), 0) * 100, 0
        ) as tp1_hit_rate,
        COALESCE(
            COUNT(CASE WHEN ob.exit_reason = 'TP2' THEN 1 END)::DECIMAL / 
            NULLIF(COUNT(CASE WHEN ob.entry_taken THEN 1 END), 0) * 100, 0
        ) as tp2_hit_rate,
        COALESCE(
            COUNT(CASE WHEN ob.exit_reason = 'TP3' THEN 1 END)::DECIMAL / 
            NULLIF(COUNT(CASE WHEN ob.entry_taken THEN 1 END), 0) * 100, 0
        ) as tp3_hit_rate,
        COALESCE(
            COUNT(CASE WHEN ob.exit_reason = 'SL' THEN 1 END)::DECIMAL / 
            NULLIF(COUNT(CASE WHEN ob.entry_taken THEN 1 END), 0) * 100, 0
        ) as sl_hit_rate,
        COALESCE(SUM(ob.pnl), 0)::DECIMAL as total_pnl,
        COALESCE(
            SUM(ob.pnl) / NULLIF(COUNT(CASE WHEN ob.entry_taken THEN 1 END), 0), 0
        )::DECIMAL as avg_pnl_per_trade
    FROM orb_sessions os
    LEFT JOIN orb_breakouts ob ON os.id = ob.orb_session_id
    WHERE os.user_id = p_user_id 
    AND (p_instrument_symbol IS NULL OR os.instrument_symbol = p_instrument_symbol);
END;
$$ LANGUAGE plpgsql;

-- Enable Row Level Security on new tables
ALTER TABLE orb_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE orb_breakouts ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for orb_sessions
CREATE POLICY "Users can view their own ORB sessions" ON orb_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own ORB sessions" ON orb_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own ORB sessions" ON orb_sessions
    FOR UPDATE USING (auth.uid() = user_id);

-- Create RLS policies for orb_breakouts
CREATE POLICY "Users can view their own ORB breakouts" ON orb_breakouts
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own ORB breakouts" ON orb_breakouts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own ORB breakouts" ON orb_breakouts
    FOR UPDATE USING (auth.uid() = user_id);

-- Add comments for documentation
COMMENT ON TABLE orb_sessions IS 'Tracks ORB (Opening Range Breakout) trading sessions';
COMMENT ON TABLE orb_breakouts IS 'Tracks individual breakout attempts within ORB sessions';
COMMENT ON FUNCTION calculate_orb_performance IS 'Calculates comprehensive ORB strategy performance metrics';

-- Insert default ORB configuration for existing users (optional)
-- UPDATE trading_configuration 
-- SET strategy_name = 'ORB'
-- WHERE strategy_name = 'EMA Scalper' AND user_id IN (
--     SELECT id FROM auth.users WHERE email LIKE '%test%'
-- );
