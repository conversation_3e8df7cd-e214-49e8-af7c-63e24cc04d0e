# ORB (Opening Range Breakout) Strategy Integration

## Overview

The ORB (Opening Range Breakout) strategy has been successfully integrated into the existing trading platform. This document provides comprehensive information about the integration, configuration options, and usage guidelines.

## What is ORB Strategy?

The Opening Range Breakout (ORB) strategy is a popular intraday trading technique that:

1. **Identifies Opening Range**: Defines a time period at market open (e.g., first 30 minutes)
2. **Tracks Range Boundaries**: Records the high and low prices during this period
3. **Waits for Breakouts**: Monitors price movements beyond these boundaries
4. **Validates Breakouts**: Requires retests to confirm genuine breakouts
5. **Manages Risk**: Uses systematic take-profit and stop-loss levels

## Integration Architecture

### Strategy Implementation
- **Location**: `src/lib/strategies/orbStrategy.ts`
- **Main Class**: `ORBStrategy`
- **Interface**: Compatible with existing `StrategyService`
- **Testing**: Comprehensive test suite in `src/__tests__/orb-strategy.test.ts`

### Key Components
1. **ORB State Machine**: Manages opening range → breakout → entry states
2. **Signal Generation**: Produces BUY/SELL/TP/SL signals
3. **Risk Management**: Multiple stop-loss and take-profit methods
4. **Session Handling**: Supports custom trading sessions

## Configuration Options

### Basic Configuration
```typescript
interface ORBConfig {
  orbTime: "15" | "30" | "45" | "60" | "90" | "120" | "180" | "240"; // minutes
  sensitivity: "High" | "Medium" | "Low" | "Lowest";
  breakoutCondition: "Close" | "EMA";
  tpMethod: "Dynamic" | "ATR";
}
```

### Advanced Configuration
```typescript
interface ORBAdvancedConfig {
  // EMA Settings (when breakoutCondition = "EMA")
  emaLength: number; // default: 9
  
  // Stop Loss Methods
  slMethod: "Safer" | "Balanced" | "Risky" | "Fixed";
  adaptiveSL: boolean; // Move SL to breakeven after TP1
  stopLossPercent: number; // For "Fixed" method
  
  // ATR-based Take Profits (when tpMethod = "ATR")
  atrTP1Mult: number; // default: 0.75
  atrTP2Mult: number; // default: 1.5
  atrTP3Mult: number; // default: 2.25
  atrPeriod: number; // default: 12
  
  // Dynamic Take Profits (when tpMethod = "Dynamic")
  minimumProfitPercent: number; // default: 0.2
  minimumProfitIncrementPercent: number; // default: 0.075
  
  // Custom Sessions
  customSessionEnabled: boolean;
  customSession: string; // format: "HHMM-HHMM", e.g., "0930-1000"
}
```

## Usage Examples

### Basic ORB Strategy Setup
```typescript
import { ORBStrategy } from '@/lib/strategies/orbStrategy';

const orbStrategy = new ORBStrategy({
  orbTime: "30", // 30-minute opening range
  sensitivity: "Medium", // Requires 1 retest
  breakoutCondition: "Close", // Use closing prices
  tpMethod: "Dynamic" // EMA-based take profits
});

const signals = orbStrategy.runStrategy(candles);
```

### Advanced Configuration
```typescript
const advancedORB = new ORBStrategy({
  orbTime: "45",
  sensitivity: "Low", // Requires 2 retests
  breakoutCondition: "EMA",
  tpMethod: "ATR",
  emaLength: 12,
  slMethod: "Balanced",
  adaptiveSL: true,
  atrTP1Mult: 1.0,
  atrTP2Mult: 2.0,
  atrTP3Mult: 3.0,
  customSessionEnabled: true,
  customSession: "0915-0945" // Custom 30-min session
});
```

### Strategy Service Integration
```typescript
import { StrategyService } from '@/lib/strategies/strategy-service';

const strategyService = new StrategyService({
  name: 'ORB',
  enabled: true,
  orbConfig: {
    orbTime: "30",
    sensitivity: "Medium",
    tpMethod: "Dynamic"
  }
});

// Switch between strategies
strategyService.updateStrategyConfig({
  name: 'EMA_SCALPER', // or 'ORB'
  enabled: true,
  emaLength: 20 // for EMA strategy
});
```

## Signal Types

The ORB strategy generates the following signal types:

1. **Entry Signals**
   - `BUY`: Bullish breakout confirmed
   - `SELL`: Bearish breakout confirmed

2. **Take Profit Signals**
   - `TP1`: First take profit level hit
   - `TP2`: Second take profit level hit
   - `TP3`: Third take profit level hit

3. **Risk Management Signals**
   - `SL`: Stop loss triggered
   - `EXIT`: Position closed (session end, etc.)

## Database Schema Changes

### New Tables
1. **orb_sessions**: Tracks opening range sessions
2. **orb_breakouts**: Records breakout attempts and outcomes

### Updated Tables
1. **trading_configuration**: Added ORB-specific parameters
2. **trading_signals**: Added ORB signal metadata
3. **strategy_performance**: Added ORB performance metrics

### Migration
Run the database migration script:
```sql
-- Execute the migration
\i scripts/orb-database-migration.sql
```

## Performance Metrics

The ORB strategy tracks comprehensive performance metrics:

- **Session Statistics**: Total sessions, breakouts, entries
- **Retest Analysis**: Average retests before entry
- **Take Profit Rates**: TP1, TP2, TP3 hit rates
- **Risk Metrics**: Stop loss hit rate, average P&L
- **Success Rates**: Entry success rate, overall profitability

## Testing

### Running ORB Tests
```bash
# Run ORB-specific tests
npm test -- --testPathPatterns=orb

# Run all strategy tests
npm test -- --testPathPatterns=strategy

# Run with coverage
npm test -- --coverage --testPathPatterns=orb
```

### Test Coverage
- ✅ Strategy initialization and configuration
- ✅ Signal generation logic
- ✅ Sensitivity settings validation
- ✅ Take profit methods (Dynamic and ATR)
- ✅ Stop loss calculations
- ✅ Custom session handling
- ✅ Error handling and edge cases
- ✅ Strategy service integration
- ✅ Performance under load

## Migration Guide

### From EMA Scalper to ORB

1. **Update Configuration**
```typescript
// Old EMA configuration
const emaConfig = {
  name: 'EMA_SCALPER',
  emaLength: 20,
  lookbackPeriod: 8
};

// New ORB configuration
const orbConfig = {
  name: 'ORB',
  orbConfig: {
    orbTime: "30",
    sensitivity: "Medium",
    tpMethod: "Dynamic"
  }
};
```

2. **Database Migration**
```sql
-- Update existing configurations
UPDATE trading_configuration 
SET strategy_name = 'ORB',
    orb_time = '30',
    sensitivity = 'Medium'
WHERE strategy_name = 'EMA_SCALPER';
```

3. **UI Updates** (when implemented)
- Strategy selection dropdown
- ORB-specific configuration panels
- Performance metrics display

## Best Practices

### Configuration Recommendations

1. **For Beginners**
```typescript
{
  orbTime: "30",
  sensitivity: "Medium",
  breakoutCondition: "Close",
  tpMethod: "Dynamic",
  slMethod: "Balanced"
}
```

2. **For Active Trading**
```typescript
{
  orbTime: "15",
  sensitivity: "High",
  breakoutCondition: "EMA",
  tpMethod: "ATR",
  slMethod: "Risky"
}
```

3. **For Conservative Trading**
```typescript
{
  orbTime: "60",
  sensitivity: "Lowest",
  breakoutCondition: "Close",
  tpMethod: "Dynamic",
  slMethod: "Safer",
  adaptiveSL: true
}
```

### Risk Management
- Always use stop losses
- Consider adaptive stop loss for better risk management
- Monitor retest patterns for strategy optimization
- Use appropriate position sizing

### Session Timing
- Standard sessions work for most markets
- Use custom sessions for specific market conditions
- Consider market volatility when setting ORB time

## Troubleshooting

### Common Issues

1. **No Signals Generated**
   - Check if sufficient candle data is available
   - Verify session timing configuration
   - Ensure breakout conditions are met

2. **Too Many False Signals**
   - Increase sensitivity level (Medium → Low → Lowest)
   - Use EMA breakout condition instead of Close
   - Adjust minimum profit thresholds

3. **Performance Issues**
   - Monitor memory usage with large datasets
   - Consider reducing candle history retention
   - Optimize database queries for large datasets

### Debug Mode
Enable debug mode for detailed logging:
```typescript
const orbStrategy = new ORBStrategy({
  // ... other config
  debug: true
});
```

## Future Enhancements

Planned improvements for the ORB strategy:

1. **Multi-Timeframe Analysis**: Support for multiple ORB timeframes
2. **Volume Confirmation**: Include volume analysis in breakout validation
3. **Market Condition Filters**: Adapt strategy based on market volatility
4. **Advanced Risk Management**: Dynamic position sizing based on ATR
5. **Machine Learning Integration**: Optimize parameters based on historical performance

## Support and Contribution

For issues, questions, or contributions related to the ORB strategy integration:

1. Check existing tests for usage examples
2. Review the strategy implementation in `src/lib/strategies/orbStrategy.ts`
3. Consult the database schema in `scripts/orb-database-migration.sql`
4. Follow the existing code patterns and testing standards

The ORB strategy integration maintains full compatibility with the existing trading platform while providing powerful new capabilities for intraday trading.
