/**
 * Strategy Service ORB Integration Tests
 * Tests the integration of ORB strategy with the strategy service
 */

import { StrategyService, StrategyConfig } from '@/lib/strategies/strategy-service';
import { ProcessedMarketData } from '@/types/dhan';

// Mock Supabase
jest.mock('@/lib/supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(() => Promise.resolve({
            data: {
              auto_trade_enabled: true,
              trading_mode: 'SANDBOX',
              strategy_name: 'ORB'
            },
            error: null
          }))
        }))
      })),
      insert: jest.fn(() => Promise.resolve({ error: null }))
    }))
  }
}));

// Mock trading logger
jest.mock('@/lib/trading-logger', () => ({
  tradingLogger: {
    logSignal: jest.fn()
  }
}));

describe('Strategy Service with ORB', () => {
  let strategyService: StrategyService;
  let orbConfig: StrategyConfig;

  beforeEach(() => {
    orbConfig = {
      name: 'ORB',
      enabled: true,
      orbConfig: {
        orbTime: "30",
        sensitivity: "Medium",
        breakoutCondition: "Close",
        tpMethod: "Dynamic",
        emaLength: 9,
        slMethod: "Balanced",
        adaptiveSL: true,
        stopLossPercent: 1.0,
        atrTP1Mult: 0.75,
        atrTP2Mult: 1.5,
        atrTP3Mult: 2.25,
        atrPeriod: 12,
        minimumProfitPercent: 0.2,
        minimumProfitIncrementPercent: 0.075,
        customSessionEnabled: false,
        customSession: "1000-1100",
        debug: false
      }
    };
    strategyService = new StrategyService(orbConfig);
  });

  describe('ORB Strategy Configuration', () => {
    it('should initialize with ORB configuration', () => {
      expect(strategyService).toBeDefined();
    });

    it('should update strategy configuration', () => {
      const newConfig: StrategyConfig = {
        name: 'ORB',
        enabled: true,
        orbConfig: {
          orbTime: "60",
          sensitivity: "High"
        }
      };
      
      expect(() => strategyService.updateStrategyConfig(newConfig)).not.toThrow();
    });
  });

  describe('Market Data Processing', () => {
    it('should process market data for ORB strategy', async () => {
      const marketData: ProcessedMarketData = {
        symbol: 'NIFTY',
        name: 'NIFTY 50',
        currentPrice: 18500,
        change: 50,
        changePercent: 0.27,
        volume: 1000000,
        lastUpdated: new Date(),
        high: 18520,
        low: 18480,
        open: 18490,
        previousClose: 18450
      };

      const userId = 'test-user-123';
      
      // Process multiple data points to build history
      for (let i = 0; i < 15; i++) {
        const testData = {
          ...marketData,
          currentPrice: marketData.currentPrice + i * 5,
          lastUpdated: new Date(Date.now() + i * 60000)
        };
        
        const signals = await strategyService.processMarketData(testData, userId);
        expect(Array.isArray(signals)).toBe(true);
      }
    });

    it('should handle insufficient data gracefully', async () => {
      const marketData: ProcessedMarketData = {
        symbol: 'BANKNIFTY',
        name: 'BANK NIFTY',
        currentPrice: 42000,
        change: 100,
        changePercent: 0.24,
        volume: 500000,
        lastUpdated: new Date(),
        high: 42050,
        low: 41950,
        open: 42000,
        previousClose: 41900
      };

      const userId = 'test-user-456';
      const signals = await strategyService.processMarketData(marketData, userId);
      
      expect(signals).toHaveLength(0); // Should return empty array for insufficient data
    });
  });

  describe('Strategy Switching', () => {
    it('should switch from EMA to ORB strategy', () => {
      // Start with EMA strategy
      const emaConfig: StrategyConfig = {
        name: 'EMA_SCALPER',
        enabled: true,
        emaLength: 20,
        lookbackPeriod: 8
      };
      
      const emaService = new StrategyService(emaConfig);
      expect(emaService).toBeDefined();
      
      // Switch to ORB strategy
      emaService.updateStrategyConfig(orbConfig);
      expect(() => emaService.updateStrategyConfig(orbConfig)).not.toThrow();
    });

    it('should handle mixed strategy parameters', () => {
      const mixedConfig: StrategyConfig = {
        name: 'ORB',
        enabled: true,
        emaLength: 20, // EMA parameter (should be ignored for ORB)
        orbConfig: {
          orbTime: "45",
          sensitivity: "Low"
        }
      };
      
      expect(() => new StrategyService(mixedConfig)).not.toThrow();
    });
  });

  describe('Signal Generation', () => {
    it('should generate ORB signals with correct format', async () => {
      const marketData: ProcessedMarketData = {
        symbol: 'NIFTY',
        name: 'NIFTY 50',
        currentPrice: 18500,
        change: 50,
        changePercent: 0.27,
        volume: 1000000,
        lastUpdated: new Date(),
        high: 18520,
        low: 18480,
        open: 18490,
        previousClose: 18450
      };

      const userId = 'test-user-789';
      
      // Build up enough data for potential signal generation
      for (let i = 0; i < 20; i++) {
        const testData = {
          ...marketData,
          currentPrice: marketData.currentPrice + i * 10,
          high: marketData.high + i * 10,
          low: marketData.low + i * 10,
          lastUpdated: new Date(Date.now() + i * 300000) // 5-minute intervals
        };
        
        await strategyService.processMarketData(testData, userId);
      }
      
      // The test verifies the service runs without errors
      // Actual signal generation depends on specific market conditions
      expect(true).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      // Mock database error
      const mockSupabase = require('@/lib/supabase').supabase;
      mockSupabase.from.mockReturnValueOnce({
        select: () => ({
          eq: () => ({
            single: () => Promise.resolve({
              data: null,
              error: { message: 'Database error' }
            })
          })
        })
      });

      const marketData: ProcessedMarketData = {
        symbol: 'NIFTY',
        name: 'NIFTY 50',
        currentPrice: 18500,
        change: 50,
        changePercent: 0.27,
        volume: 1000000,
        lastUpdated: new Date(),
        high: 18520,
        low: 18480,
        open: 18490,
        previousClose: 18450
      };

      const userId = 'test-user-error';
      const signals = await strategyService.processMarketData(marketData, userId);
      
      expect(signals).toHaveLength(0); // Should handle error gracefully
    });

    it('should handle invalid market data', async () => {
      const invalidMarketData = {
        symbol: '',
        name: '',
        currentPrice: NaN,
        change: NaN,
        changePercent: NaN,
        volume: -1,
        lastUpdated: new Date(),
        high: NaN,
        low: NaN,
        open: NaN,
        previousClose: NaN
      } as ProcessedMarketData;

      const userId = 'test-user-invalid';
      
      expect(async () => {
        await strategyService.processMarketData(invalidMarketData, userId);
      }).not.toThrow();
    });
  });

  describe('Performance', () => {
    it('should handle high-frequency data updates', async () => {
      const marketData: ProcessedMarketData = {
        symbol: 'NIFTY',
        name: 'NIFTY 50',
        currentPrice: 18500,
        change: 50,
        changePercent: 0.27,
        volume: 1000000,
        lastUpdated: new Date(),
        high: 18520,
        low: 18480,
        open: 18490,
        previousClose: 18450
      };

      const userId = 'test-user-performance';
      const startTime = Date.now();
      
      // Process 100 rapid updates
      for (let i = 0; i < 100; i++) {
        const testData = {
          ...marketData,
          currentPrice: marketData.currentPrice + Math.random() * 10,
          lastUpdated: new Date(Date.now() + i * 1000)
        };
        
        await strategyService.processMarketData(testData, userId);
      }
      
      const endTime = Date.now();
      const processingTime = endTime - startTime;
      
      // Should complete within reasonable time (adjust threshold as needed)
      expect(processingTime).toBeLessThan(5000); // 5 seconds
    });
  });
});
