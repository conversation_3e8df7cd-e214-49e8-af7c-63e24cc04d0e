/**
 * ORB Strategy Configuration Component
 * Provides UI for configuring ORB strategy parameters
 */

'use client';

import React, { useState } from 'react';
import { ORBConfig } from '@/lib/strategies/orbStrategy';

interface ORBStrategyConfigProps {
  config: ORBConfig;
  onConfigChange: (config: ORBConfig) => void;
  disabled?: boolean;
}

export function ORBStrategyConfig({ 
  config, 
  onConfigChange, 
  disabled = false 
}: ORBStrategyConfigProps) {
  const [localConfig, setLocalConfig] = useState<ORBConfig>(config);

  const handleChange = (field: keyof ORBConfig, value: any) => {
    const newConfig = { ...localConfig, [field]: value };
    setLocalConfig(newConfig);
    onConfigChange(newConfig);
  };

  return (
    <div className="space-y-6 p-6 bg-white rounded-lg shadow-sm border">
      <div className="border-b pb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          ORB Strategy Configuration
        </h3>
        <p className="text-sm text-gray-600 mt-1">
          Configure Opening Range Breakout strategy parameters
        </p>
      </div>

      {/* Basic Settings */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* ORB Time */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Opening Range Time
          </label>
          <select
            value={localConfig.orbTime || "30"}
            onChange={(e) => handleChange('orbTime', e.target.value)}
            disabled={disabled}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
          >
            <option value="15">15 minutes</option>
            <option value="30">30 minutes</option>
            <option value="45">45 minutes</option>
            <option value="60">60 minutes</option>
            <option value="90">90 minutes</option>
            <option value="120">120 minutes</option>
            <option value="180">180 minutes</option>
            <option value="240">240 minutes</option>
          </select>
        </div>

        {/* Sensitivity */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Breakout Sensitivity
          </label>
          <select
            value={localConfig.sensitivity || "Medium"}
            onChange={(e) => handleChange('sensitivity', e.target.value)}
            disabled={disabled}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
          >
            <option value="High">High (0 retests)</option>
            <option value="Medium">Medium (1 retest)</option>
            <option value="Low">Low (2 retests)</option>
            <option value="Lowest">Lowest (3 retests)</option>
          </select>
        </div>

        {/* Breakout Condition */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Breakout Condition
          </label>
          <select
            value={localConfig.breakoutCondition || "Close"}
            onChange={(e) => handleChange('breakoutCondition', e.target.value)}
            disabled={disabled}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
          >
            <option value="Close">Close Price</option>
            <option value="EMA">EMA Crossover</option>
          </select>
        </div>

        {/* Take Profit Method */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Take Profit Method
          </label>
          <select
            value={localConfig.tpMethod || "Dynamic"}
            onChange={(e) => handleChange('tpMethod', e.target.value)}
            disabled={disabled}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
          >
            <option value="Dynamic">Dynamic (EMA-based)</option>
            <option value="ATR">ATR-based</option>
          </select>
        </div>
      </div>

      {/* EMA Settings (when using EMA breakout condition) */}
      {localConfig.breakoutCondition === "EMA" && (
        <div className="border-t pt-4">
          <h4 className="text-md font-medium text-gray-800 mb-3">EMA Settings</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                EMA Length
              </label>
              <input
                type="number"
                value={localConfig.emaLength || 9}
                onChange={(e) => handleChange('emaLength', parseInt(e.target.value))}
                disabled={disabled}
                min="1"
                max="100"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
              />
            </div>
          </div>
        </div>
      )}

      {/* Stop Loss Settings */}
      <div className="border-t pt-4">
        <h4 className="text-md font-medium text-gray-800 mb-3">Stop Loss Settings</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Stop Loss Method
            </label>
            <select
              value={localConfig.slMethod || "Balanced"}
              onChange={(e) => handleChange('slMethod', e.target.value)}
              disabled={disabled}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
            >
              <option value="Safer">Safer (Conservative)</option>
              <option value="Balanced">Balanced</option>
              <option value="Risky">Risky (Aggressive)</option>
              <option value="Fixed">Fixed Percentage</option>
            </select>
          </div>

          {localConfig.slMethod === "Fixed" && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Stop Loss Percentage
              </label>
              <input
                type="number"
                value={localConfig.stopLossPercent || 1.0}
                onChange={(e) => handleChange('stopLossPercent', parseFloat(e.target.value))}
                disabled={disabled}
                min="0.1"
                max="10"
                step="0.1"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
              />
            </div>
          )}

          <div className="md:col-span-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={localConfig.adaptiveSL || false}
                onChange={(e) => handleChange('adaptiveSL', e.target.checked)}
                disabled={disabled}
                className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50"
              />
              <span className="text-sm text-gray-700">
                Adaptive Stop Loss (Move to breakeven after TP1)
              </span>
            </label>
          </div>
        </div>
      </div>

      {/* ATR Settings (when using ATR take profit method) */}
      {localConfig.tpMethod === "ATR" && (
        <div className="border-t pt-4">
          <h4 className="text-md font-medium text-gray-800 mb-3">ATR Take Profit Settings</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                TP1 Multiplier
              </label>
              <input
                type="number"
                value={localConfig.atrTP1Mult || 0.75}
                onChange={(e) => handleChange('atrTP1Mult', parseFloat(e.target.value))}
                disabled={disabled}
                min="0.1"
                max="5"
                step="0.25"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                TP2 Multiplier
              </label>
              <input
                type="number"
                value={localConfig.atrTP2Mult || 1.5}
                onChange={(e) => handleChange('atrTP2Mult', parseFloat(e.target.value))}
                disabled={disabled}
                min="0.1"
                max="5"
                step="0.25"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                TP3 Multiplier
              </label>
              <input
                type="number"
                value={localConfig.atrTP3Mult || 2.25}
                onChange={(e) => handleChange('atrTP3Mult', parseFloat(e.target.value))}
                disabled={disabled}
                min="0.1"
                max="5"
                step="0.25"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                ATR Period
              </label>
              <input
                type="number"
                value={localConfig.atrPeriod || 12}
                onChange={(e) => handleChange('atrPeriod', parseInt(e.target.value))}
                disabled={disabled}
                min="5"
                max="50"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
              />
            </div>
          </div>
        </div>
      )}

      {/* Dynamic TP Settings (when using Dynamic take profit method) */}
      {localConfig.tpMethod === "Dynamic" && (
        <div className="border-t pt-4">
          <h4 className="text-md font-medium text-gray-800 mb-3">Dynamic Take Profit Settings</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Minimum Profit % (for TP1)
              </label>
              <input
                type="number"
                value={localConfig.minimumProfitPercent || 0.2}
                onChange={(e) => handleChange('minimumProfitPercent', parseFloat(e.target.value))}
                disabled={disabled}
                min="0.1"
                max="2"
                step="0.1"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Profit Increment % (TP2/TP3)
              </label>
              <input
                type="number"
                value={localConfig.minimumProfitIncrementPercent || 0.075}
                onChange={(e) => handleChange('minimumProfitIncrementPercent', parseFloat(e.target.value))}
                disabled={disabled}
                min="0.01"
                max="1"
                step="0.025"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
              />
            </div>
          </div>
        </div>
      )}

      {/* Custom Session Settings */}
      <div className="border-t pt-4">
        <h4 className="text-md font-medium text-gray-800 mb-3">Session Settings</h4>
        <div className="space-y-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={localConfig.customSessionEnabled || false}
              onChange={(e) => handleChange('customSessionEnabled', e.target.checked)}
              disabled={disabled}
              className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50"
            />
            <span className="text-sm text-gray-700">
              Use Custom Session Time
            </span>
          </label>

          {localConfig.customSessionEnabled && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Custom Session (HHMM-HHMM)
              </label>
              <input
                type="text"
                value={localConfig.customSession || "1000-1100"}
                onChange={(e) => handleChange('customSession', e.target.value)}
                disabled={disabled}
                placeholder="0930-1000"
                pattern="[0-9]{4}-[0-9]{4}"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
              />
              <p className="text-xs text-gray-500 mt-1">
                Format: HHMM-HHMM (e.g., 0930-1000 for 9:30 AM to 10:00 AM)
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Debug Mode */}
      <div className="border-t pt-4">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={localConfig.debug || false}
            onChange={(e) => handleChange('debug', e.target.checked)}
            disabled={disabled}
            className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50"
          />
          <span className="text-sm text-gray-700">
            Enable Debug Mode (Detailed Logging)
          </span>
        </label>
      </div>
    </div>
  );
}
